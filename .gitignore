# 依赖目录
node_modules/
*/node_modules/
**/node_modules/

# 构建输出
dist/
build/
*/dist/
*/build/
**/dist/
**/build/

# 包管理器文件
package-lock.json
yarn.lock
*/package-lock.json
*/yarn.lock
**/package-lock.json
**/yarn.lock

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output

# 依赖目录
jspm_packages/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微包
.pnp
.pnp.js

# 测试覆盖率
coverage/

# 生产构建
/build

# 临时文件夹
tmp/
temp/

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 本地配置文件
*.local

# 缓存目录
.cache/
.parcel-cache/

# 其他
*.tgz
*.tar.gz
*.tar.bz2
*.tar.xz

# 保留特定构建文件（如果需要的话）
!thisfuns/build/
!wowlive/static/
!wowlive-old/static/