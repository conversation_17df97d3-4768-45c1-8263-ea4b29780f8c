# Wow Website Projects

这是一个包含多个公司官网项目的web项目集合，使用Git进行版本管理。

## 项目结构

项目包含以下子项目：

### Vue.js 项目
- `tapfun/` - Vue.js项目
- `xinyichengTech/` - Vue.js项目

### React 项目  
- `woweds.com/` - React项目
- `thisfuns/` - React项目

### 静态网站项目
- `callme/` - 静态HTML网站
- `camcalls/` - 隐私政策和服务条款页面
- `camu/` - 静态HTML网站
- `chatnow/` - 静态HTML网站
- `easechats/` - H5下载页面
- `joichat/` - 静态HTML网站
- `joijoy/` - 静态HTML网站
- `joychat/` - 静态HTML网站
- `livideocall/` - 静态HTML网站
- `moodmate/` - 静态HTML网站
- `omechat/` - 静态HTML网站
- `webcamapp/` - 静态HTML网站
- `wowed/` - 静态HTML网站
- `wowlive/` - 静态HTML网站
- `wowlive-old/` - 静态HTML网站
- `wowpro/` - 静态HTML网站
- `百猎/` - 静态HTML网站
- `中文模版/` - 隐私政策和服务条款模板

## Git 管理策略

### 忽略的文件类型

项目配置了全面的 `.gitignore` 文件，忽略以下类型的文件：

#### 依赖文件
- `node_modules/` - Node.js依赖包
- `package-lock.json` - npm锁定文件
- `yarn.lock` - Yarn锁定文件

#### 构建输出
- `dist/` - 构建输出目录
- `build/` - 构建输出目录

#### 压缩文件
- `*.zip` - ZIP压缩文件
- `*.rar` - RAR压缩文件
- `*.7z` - 7-Zip压缩文件
- `*.tar` - TAR压缩文件
- `*.tar.gz` - GZIP压缩文件
- `*.tar.bz2` - BZIP2压缩文件

#### 日志和缓存
- `*.log` - 日志文件
- `.cache/` - 缓存目录
- `.eslintcache` - ESLint缓存

#### IDE和编辑器文件
- `.vscode/` - VS Code配置
- `.idea/` - IntelliJ IDEA配置
- `*.swp` - Vim临时文件

#### 操作系统文件
- `.DS_Store` - macOS系统文件
- `Thumbs.db` - Windows缩略图文件

#### 环境变量
- `.env*` - 环境变量文件

### 保留的文件

以下构建文件被保留在版本控制中：
- `thisfuns/build/` - 生产构建文件
- `wowlive/static/` - 静态资源文件
- `wowlive-old/static/` - 静态资源文件

## 开发指南

### 项目管理脚本

项目提供了两个管理脚本：

#### 状态检查脚本
```bash
./scripts/status.sh
```
显示所有项目的状态，包括：
- Git 状态
- 被忽略的文件
- 各个项目的完整性检查

#### 清理脚本
```bash
./scripts/clean.sh
```
清理项目中的临时文件和构建文件：
- 删除所有 node_modules 目录
- 删除 dist 和 build 目录
- 删除日志和临时文件
- 删除压缩文件
- 删除操作系统生成的文件

**注意：** 运行清理脚本后需要重新安装依赖和构建项目。

### 添加新项目
1. 在根目录创建新的项目文件夹
2. 确保项目遵循现有的命名规范
3. 更新此README文件的项目列表

### 提交代码
```bash
# 添加所有更改
git add .

# 提交更改
git commit -m "描述你的更改"

# 推送到远程仓库
git push origin main
```

### 分支管理
- `main` - 主分支，包含稳定的代码
- `develop` - 开发分支，用于新功能开发
- 功能分支 - 从 `develop` 分支创建，用于特定功能开发

## 注意事项

1. 确保在提交前运行 `git status` 检查要提交的文件
2. 不要提交敏感信息（如API密钥、密码等）
3. 定期清理不需要的文件和目录
4. 保持项目结构的一致性

## 联系信息

如有问题或建议，请联系项目维护者。 