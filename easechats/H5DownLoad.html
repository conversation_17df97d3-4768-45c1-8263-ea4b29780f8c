<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Download Easechat</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/UAParser.js/0.7.20/ua-parser.min.js" integrity="sha512-70OZ+iUuudMmd7ikkUWcEogIw/+MIE17z17bboHhj56voxuy+OPB71GWef47xDEt5+MxFdrigC1tRDmuvIVrCA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://www.itxst.com/package/clipboardjs/clipboard.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js" integrity="sha512-a+SUDuwNzXDvz4XrIcXHuCf089/iJAoN4lmrXJg18XnduKK6YlDHNRalv4yd1N40OKI80tFidF+rqTFKGPoWFQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <style>
    html,
    body {
      width: 100%;
      height: 100%;
      background: #ff3f58;
      margin: 0;
      padding: 0;
    }

    .box {
      width: 100%;
      min-height: 100%;
      background: #ff3f58;

      /* background: url('./image/bg.png'), no-repeat;
      background-size: cover;
      background-position: center top; */
    }

    .btn {
      width: 100%;
      position: fixed;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0.36rem;
    }

    img {
      width: 100%;
    }
  </style>
</head>
<script>
  (function (doc, win) {
    var docEl = doc.documentElement,
      resizeEvt =
        "orientationchange" in window ? "orientationchange" : "resize",
      recalc = function () {
        var clientWidth = docEl.clientWidth;

        if (!clientWidth) return;

        docEl.style.fontSize = (clientWidth / 375) * 40 + "px";
      };

    if (!doc.addEventListener) return;

    win.addEventListener(resizeEvt, recalc, false);

    doc.addEventListener("DOMContentLoaded", recalc, false);
  })(document, window);

</script>
<body>
  <div class="box">
    <img src="./img/en_bg.png" alt="" id="img" />
    <div class="btn" id="btn" @click="downLoad">
      <img src="./img/en_btn.png" id="img1" alt="" />
    </div>
  </div>
</body>
<script src="./js/main.js"></script>
</html>