!(function (f, b, e, v, n, t, s) {
  if (f.fbq) return;
  n = f.fbq = function () {
    n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
  };
  if (!f._fbq) f._fbq = n;
  n.push = n;
  n.loaded = !0;
  n.version = "2.0";
  n.queue = [];
  t = b.createElement(e);
  t.async = !0;
  t.src = v;
  s = b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t, s);
})(
  window,
  document,
  "script",
  "https://connect.facebook.net/en_US/fbevents.js"
);
fbq("init", "1755182418575248");
fbq("track", "PageView");

function getUrl() {
  const arr = window.location.href.split("?");
  if (arr.length > 1) {
    const queryParams = arr[1].split("&");
    const paramsObject = {};
    queryParams.forEach((param) => {
      const [key, value] = param.split("=");
      paramsObject[key] = decodeURIComponent(value);
    });
    return paramsObject;
  }
  return null;
}

let param = getUrl();

let Tools = {
  adPlatform: "Meta",
  eventSourceUrl: window.location.href,
  fingerprinting: {
    appChannel: "",
    platform: "",
    language: "",
    resolution: "",
    resolutionRatio: "",
    timeZone: "",
  },
  facebookEvent: {
    event_name: "StartTrial",
    event_time: getUtcTime(),
    event_source_url: window.location.href,
    user_data: {
      client_ip_address: "",
      client_user_agent: window.navigator.userAgent,
      fbc: "",
      fbp: "",
      country_code: "",
    },
    action_source: "website",
  },

  getUserAgent() {
    let navigator = window.navigator;
    let parser = new UAParser(navigator.userAgent);

    this.parseNavigator = parser.getResult();
    let language = navigator.language;
    this.fingerprinting.resolution =
      window.screen.width + ":" + window.screen.height;
    this.fingerprinting.timeZone =
      Intl.DateTimeFormat().resolvedOptions().timeZone;

    this.fingerprinting.resolutionRatio = Number(
      window.devicePixelRatio
    ).toFixed(2);

    let splitArr = language.split("-");
    if (splitArr.length === 3) {
      splitArr.splice(1, 1);
    }
    language = splitArr.join("-");
    this.fingerprinting.language = splitArr[0];
  },
  async init(query) {
    let ua = window.navigator.userAgent || "";
    let isAndroid = /android/i.test(ua);
    let isIos = /iphone|ipad|ipod/i.test(ua);
    this.fingerprinting.platform = isAndroid ? "Android" : isIos ? "iOS" : "";

    await this.getUserAgent();
    this.facebookEvent.user_data.fbc =
      query?.fbcid ||
      getCookie("_fbc") ||
      `fb.1.${+new Date()}.${query?.fbclid}`;
    this.facebookEvent.user_data.fbp = query?.fbpid || getCookie("_fbp");

    this.fingerprinting.appChannel = query?.appChannel;

    return this;
  },
};

function encrypt(data, key = "com.novel.manga") {
  var key = CryptoJS.enc.Utf8.parse(getKey(key));
  let encrypted = CryptoJS.AES.encrypt(data, key, {
    iv: CryptoJS.enc.Utf8.parse("5873087691895047"),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

function getKey(key) {
  return (key + "0000000000000000").substring(0, 16);
}

function getUtcTime() {
  var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
  var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
  var targetDate = new Date(nowDate + offset_GMT * 60 * 1000); //当前东八区的时间
  var current = Math.floor(targetDate.getTime() / 1000); //当前时区时间戳
  return current;
}

function copyTextToClipboard(input, { target = document.body } = {}) {
  let clipboard = new ClipboardJS("#btn", {
    text: function (trigger) {
      //返回字符串
      return input;
    },
  });
  clipboard.on("success", (e) => {
    clipboard.destroy();
  });
  clipboard.on("error", (e) => {
    clipboard.destroy();
  });
}
function getCookie(name) {
  let arr,
    reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
  if ((arr = document.cookie.match(reg))) return unescape(arr[2]);
  else return "";
}

document.querySelector("#btn").addEventListener("click", () => {
  window.fbq("track", "StartTrial");
  downLoad("StartTrial");
  if (param.url) {
    window.open(param.url);
  }
});

Tools.init(param);
function downLoad(type) {
  const obj = {
    adPlatform: Tools.adPlatform,
    eventSourceUrl: Tools.eventSourceUrl,
    fingerprinting: Tools.fingerprinting,
    facebookEvent: Tools.facebookEvent,
  };
  obj.facebookEvent.event_name = type;
  const copy = encrypt(JSON.stringify(obj));
  copyTextToClipboard(copy);
  fetch(`https://t-api.woweds.com/account_service/w2p/send`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(obj),
  })
    .then((response) => response.json())
    .then((data) => {})
    .catch(() => {
      localStorage.setItem("location", "US");
    });
}

window.addEventListener("load", () => {
  let language = "en";
  if (navigator.languages && navigator.languages.length > 0) {
    // 对于支持 navigator.languages 的现代浏览器
    language = navigator.languages[0];
  } else if (navigator.language) {
    // 对于支持 navigator.language 的浏览器
    language = navigator.language;
  } else if (navigator.userLanguage) {
    // 对于 Internet Explorer (版本低于 11)
    language = navigator.userLanguage;
  }

  const img = document.querySelector("#img");
  const img1 = document.querySelector("#img1");
  const type = ["en", "es", "pt", "tr", "ae"].includes(language.toLowerCase());
  const language1 = type ? language.toLowerCase() : "en";
  img.src = `./img/${language1}_bg.png`;
  img1.src = `./img/${language1}_btn.png`;
  downLoad("ViewContent");
});
