<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="appstore:developer_url" content="https://apps.apple.com/hk/app/wow-match-live-video-chats/id1579018518">
  <meta name="appstore:bundle_id" content="com.wow.chat.ios">
  <meta name="appstore:store_id" content="1579018518">
  <link rel="shortcut icon" href="./favicon.ico">
  <title>Livideocall-Live Video Chat</title>
  <link rel="stylesheet" href="./css/swiper-bundle.min.css">
</head>
<script>
  (function (doc, win) {

    var docEl = doc.documentElement,

      resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',

      recalc = function () {

        var clientWidth = docEl.clientWidth;

        if (!clientWidth) return;

        docEl.style.fontSize = 20 * (clientWidth / 375) + 'px';

      };

    if (!doc.addEventListener) return;

    win.addEventListener(resizeEvt, recalc, false);

    doc.addEventListener('DOMContentLoaded', recalc, false);

  })(document, window);
</script>
<style>
  * {
    margin: 0;
    padding: 0;
  }

  html,
    body {
      font-family: Roboto-Regular, Roboto;
      position: relative;
      height: 100%;
    }
  .swiper-container {
      width: 100%;
      height: 100%;
    }
    .swiper-wrapper{
      width: 100%;
      height: 100%;
      position: absolute;
    }

    .swiper-slide {
      text-align: center;
      font-size: 18px;
      background: #fff;
      /* Center slide text vertically */
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
    }
  .wowed{
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 10,  0.76);
    z-index: 3;
    color: #fff;
  }
  .wowedGroup{
    padding-left: 0.6rem;
    padding-top: 0.3rem;
    font-size: 0.3rem;
    display: flex;
    align-items: center;
  }
  /* .wowedGroup .span1{
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    background: url('./imgs/logo.png') no-repeat;
    background-size: contain;
  } */
  .wowedGroup .span2{
    font-weight: bold;
    padding-left: 0.2rem;
  }
  .wowedGroup .span3{
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
  }
  .wowedlogo{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    text-align: center;
  }
  .wow{
    font-size: 1.4rem;
    font-weight: bold;
  }
  .wowImg{
    display: flex;
    justify-content: center;
    margin-top: 0.2rem;

  }
  .wowImg .img1,.img2{
    width: 3.3rem;
    height: 1.06rem;
    padding-left: 0.2rem;
  }
  .img2{
    width: 3.5rem;
  }
  .wowvoice{
    font-size: 0.56rem;
    margin-top: 0.4rem;
  }
  .wowworld{
    font-size: 0.4rem;
    color: rgba(255, 255, 255, 0.4);
    margin-top: 0.12rem;
  }
  .copyright{
    width: 100%;
    position: absolute;
    bottom: 0;
    font-size: 0.22rem;
    display: flex;
    justify-content: space-between;
    padding: 0.30rem 0;
    color: rgba(255, 255, 255, 0.7);
  }
  .contact{
    display: flex;
    justify-content: flex-start;
    padding-left: 0.6rem;
  }
  .contact div{
    margin-right: 0.9rem;
  }
  .contact a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    
  }
  .contact a:hover{
      color: #fff;
    }
  .rights{
    padding-right: 0.6rem;
  }
</style>
<body> 
  
  <script src="./js/swiper-bundle.min.js"></script>
  <script src="./js/main.js"></script>
  <script language="javascript">
    var swiper = new Swiper('.swiper-container', {
      autoplay:true,
      effect : 'fade',
      fadeEffect: {
        crossFade: true,
      }
    });
    
  </script>
  <script>
    // var link = document.createElement("link");//先获取link标签对象
    // link.rel = "shortcut icon";//设置rel类型
    // link.href = "./favicon.ico";//设置href链接
    // document.head.appendChild(link);//输出修改
    // console.log(document.getElementsByTagName('title')[0].innerText);
    // console.log(window.location.hostname);
    var websiteUrl = window.location.href
    if (websiteUrl == 'https://livideocall.com/' || websiteUrl == 'https://livideocall.com/index.html' || websiteUrl == 'https://www.livideocall.com') {
      window.location.href = '/index.html'
    }
  </script>
</body>

</html>