#!/bin/bash

# 清理脚本 - 删除不需要的文件和目录

echo "开始清理项目..."

# 删除所有 node_modules 目录
echo "删除 node_modules 目录..."
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true

# 删除所有 dist 和 build 目录
echo "删除 dist 和 build 目录..."
find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true

# 删除日志文件
echo "删除日志文件..."
find . -name "*.log" -type f -delete 2>/dev/null || true

# 删除临时文件
echo "删除临时文件..."
find . -name "*.tmp" -type f -delete 2>/dev/null || true
find . -name "*.cache" -type f -delete 2>/dev/null || true

# 删除压缩文件
echo "删除压缩文件..."
find . -name "*.zip" -type f -delete 2>/dev/null || true
find . -name "*.rar" -type f -delete 2>/dev/null || true
find . -name "*.7z" -type f -delete 2>/dev/null || true
find . -name "*.tar" -type f -delete 2>/dev/null || true
find . -name "*.tar.gz" -type f -delete 2>/dev/null || true
find . -name "*.tar.bz2" -type f -delete 2>/dev/null || true

# 删除操作系统生成的文件
echo "删除操作系统文件..."
find . -name ".DS_Store" -type f -delete 2>/dev/null || true
find . -name "Thumbs.db" -type f -delete 2>/dev/null || true

# 删除 IDE 配置文件
echo "删除 IDE 配置文件..."
find . -name ".vscode" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".idea" -type d -exec rm -rf {} + 2>/dev/null || true

echo "清理完成！"
echo ""
echo "注意："
echo "- 如果某些项目需要保留构建文件，请手动恢复"
echo "- 运行 'npm install' 或 'yarn install' 重新安装依赖"
echo "- 运行 'npm run build' 或 'yarn build' 重新构建项目" 