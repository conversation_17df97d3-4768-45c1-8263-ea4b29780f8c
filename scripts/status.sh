#!/bin/bash

# 项目状态检查脚本

echo "=== Wow Website Projects 状态检查 ==="
echo ""

# 检查 Git 状态
echo "📊 Git 状态:"
git status --short
echo ""

# 检查被忽略的文件
echo "🚫 被忽略的文件:"
git status --ignored | grep -E "\.(zip|rar|7z|tar|log|tmp|cache)$|node_modules|\.DS_Store|\.vscode|\.idea" | head -10
echo ""

# 检查各个项目
echo "📁 项目状态:"

# Vue.js 项目
echo "🎨 Vue.js 项目:"
for project in tapfun xinyichengTech; do
    if [ -d "$project" ]; then
        if [ -f "$project/package.json" ]; then
            echo "  ✅ $project - 存在 package.json"
        else
            echo "  ⚠️  $project - 缺少 package.json"
        fi
    else
        echo "  ❌ $project - 目录不存在"
    fi
done

# React 项目
echo ""
echo "⚛️  React 项目:"
for project in woweds.com thisfuns; do
    if [ -d "$project" ]; then
        if [ -f "$project/package.json" ]; then
            echo "  ✅ $project - 存在 package.json"
        else
            echo "  ⚠️  $project - 缺少 package.json"
        fi
    else
        echo "  ❌ $project - 目录不存在"
    fi
done

# 静态网站项目
echo ""
echo "🌐 静态网站项目:"
static_projects=("callme" "camcalls" "camu" "chatnow" "easechats" "joichat" "joijoy" "joychat" "livideocall" "moodmate" "omechat" "webcamapp" "wowed" "wowlive" "wowlive-old" "wowpro" "百猎" "中文模版")

for project in "${static_projects[@]}"; do
    if [ -d "$project" ]; then
        if [ -f "$project/index.html" ]; then
            echo "  ✅ $project - 存在 index.html"
        else
            echo "  ⚠️  $project - 缺少 index.html"
        fi
    else
        echo "  ❌ $project - 目录不存在"
    fi
done

echo ""
echo "=== 检查完成 ===" 