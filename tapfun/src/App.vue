<template>
  <div id="app">
    <router-view/>
  </div>
</template>
<script>
	export default{
		mounted(){
			let vh = window.innerHeight * 0.01
			document.documentElement.style.setProperty('--vh', `${vh}px`)
			
			
			window.addEventListener('resize', () => {
			  let vh = window.innerHeight * 0.01
			  document.documentElement.style.setProperty('--vh', `${vh}px`)
			})
			
		}
	}
</script>
<style>
#app{
	width: 100%;
	height: 100%;
}
</style>
