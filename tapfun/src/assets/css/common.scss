@use "sass:math";

@font-face {
  font-family: "Akrobat-Black";
  src: url('../font/Akrobat-Black.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Akrobat-Regular";
  src: url('../font/Akrobat-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "PingFang-Heavy";
  src: url('../font/PingFang Heavy.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "PingFang Regular";
  src: url('../font/PingFang Regular.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.page{
	overflow: hidden;
	@media(max-width:1024px){
		// overflow-y:auto;
	}
}
$position:(
	relative,
	absolute,
	fixed
) !default;

$colors:(
	white:#fff,
	primary:var(--primary-color),
	black:#2A2A2A
) !default;

@each $type in $position{
	.#{$type}{
		position:#{$type};
	}
}

@each $color,$value in $colors{
	.text-#{"" + $color}{
		color:#{$value};
	}
}


@for $i from 0 through 130 {
    .pl#{$i}{ padding-left:#{math.div($i,100)}rem; }
    .w#{$i}{ width:#{math.div($i,100)}rem; }
    .h#{$i}{ height:#{math.div($i,100)}rem; }
    .pr#{$i}{ padding-right:#{math.div($i,100)}rem; }
    .pt#{$i}{ padding-top:#{math.div($i,100)}rem; }
    .pb#{$i}{ padding-bottom:#{math.div($i,100)}rem; }
    .ml#{$i}{ margin-left:#{math.div($i,100)}rem; }
    .mt#{$i}{ margin-top:#{math.div($i,100)}rem; }
    .mr#{$i}{ margin-right:#{math.div($i,100)}rem; }
    .mb#{$i}{ margin-bottom:#{math.div($i,100)}rem; }
    .fz#{$i}{ font-size:#{math.div($i,100)}rem; }
    .lh#{$i}{ line-height:#{math.div($i,100)}rem; }
    .rad#{$i}{ border-radius:#{math.div($i,100)}rem; }
    .top#{$i}{ top:#{math.div($i,100)}rem; }
    .bottom#{$i}{ bottom:#{math.div($i,100)}rem; }
}

.ml360{
	margin-left: 3.6rem;
}
.mr360{
	margin-right: 3.6rem;
}


.pl360{
	padding-left: 3.6rem;
}
.pr360{
	padding-right: 3.6rem;
}

.dib{
	display: inline-block;
	vertical-align: middle;
}

html{
    --primary-color:#EB7D15;
	line-height: 1;
	user-select: none;
	-webkit-tap-highlight-color:transparent;
    font-size: 100px;
    // @media (min-width: 1100px) {
    //     font-size:84px;
    // }
    // @media (max-width: 1500px) {
    //     font-size: 90px;
    // }
    @media(max-width:980px){
        font-size:13.33vw;
    }
}
:focus{
	outline:none;
}
body{
	font-size:16px;
}

.flex{
    display:flex;
    flex-wrap:wrap;
}
.oh{
	overflow: hidden;
}

.flex1{
    flex:1;
}

.justify-center{
    justify-content: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-end{
    justify-content:flex-end;
}
.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.items-center{
    align-items: center;
}

.flex-center{
    align-items:center;
    justify-content: center;
}

.f-column{
    flex-direction: column;
}




.pad{
    padding:0 2.6%;
}


.pad-lg{
    padding-left:8%;
    padding-right:8%;
}

.w253{
    width:2.53rem;
}
.ccc{
    color:#ccc;
}

.primaryText{
    color:var(--primary-color)
}

.hover-primary{
    &:hover{
        color:var(--primary-color)
    }
}

.fw400{
    font-weight: 400;
}
.fw500{
    font-weight: 500;
}
.bold{
	font-weight: bold;
}
.fw800{
    font-weight: 800;
}
.block-center {
	margin-left: auto;
	margin-right: auto;
}

.cursor {
	cursor: pointer;
}

.full-width {
	width: 100%;
}
.full-height{
	height:100%;
}

.w1080{
	width:10.8rem;
	margin-left: auto;
	margin-right: auto;
}
.window-height {
	height: calc(var(--vh, 1vh) * 100);
}
textarea{
	resize:none !important;
}

.window-width {
	width:100%;
}



.oh {
	overflow: hidden;
}

.ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.tac {
	text-align: center;
}

.sizing {
	box-sizing: border-box;
}



.text-grey{
    color:#666;
}

.opacity8{
	opacity:0.8;
}
.tar{
    text-align: right;
}

.ffpb{
	font-family: "Poppins-Bold";
}

.ffpr{
	font-family: "Poppins-Regular";
}

.ffpf{
	font-family: "PingFang Regular";
}

.opacity49{
	opacity:0.49;
}

.page-title{
	font-size: 0.6rem;
	// font-weight: 800;
	color: #FFFFFF;
	line-height: 0.84rem;
	font-family: "PingFang-Heavy";
	display: inline-block;
	position: relative;
	&:after{
		content:"";
		display: block;
		width:100%;
		height:16px;
		position:absolute;
		left:0;
		bottom:0.08rem;
		border-radius:16px;
		background: rgba(#D8D8D8,0.1);
	}
	span{
		font-size: 0.28rem;
		font-family: "Akrobat-Regular";
		font-weight: 400;
		color: #FFFFFF;
		text-transform:uppercase;
		display: inline-block;
		vertical-align: -0.05rem;
		position:relative;
		z-index:10;
		opacity:0.5;
		margin-left: 0.22rem;
		line-height: 0.34rem;
	}


	&.white-bg{
		color:#2A2A2A;
		&:after{
			background: rgba(#000,0.1);
		}
		span{
			color:#2A2A2A;
		}
	}
	@media(min-height:0px) and (min-width:1024px){
		font-size:0.46rem;
		&:after{
			height: 0.1rem;
			bottom:0.2rem;
		}
		span{
			font-size:0.28rem;
			line-height:0.28rem;
		}
	}
	@media(max-width:1024px){
		font-size:0.38rem;
		line-height:0.52rem;
		span{
			font-size:0.2rem;
			margin-left: 0.09rem;
			line-height: 0.24rem;
		}
		&:after{
			height: 0.12rem;
		}
	}
}


.l-inner{
	padding-left: 18.75%;
	padding-right: 18.75%;
	@media(max-width:1600px){
		padding-left: 14%;
		padding-right: 14%;
	}
	@media(max-width: 1300px) {
		padding-left: 12%;
		padding-right: 12%;
	}
	@media(max-width:1024px){
		padding-left:0.5rem;
		padding-right:0.5rem;
	}
}

.amap-copyright,.amap-logo{
	display: none !important;
}

.navBox{
	position:fixed;
	left:0;
	top:0;
	width:100%;
	height:100%;
	background-image:url('~@/assets/images/page2bg.png');
	background-repeat: repeat;
	background-size:0.1rem 0.1rem;
	display:flex;
	align-items:center;
	justify-content: center;
	flex-direction: column;
	a{
		display: block;
		font-size: 0.32rem;
		font-family: "PingFang Regular";
		font-weight: 400;
		line-height: 0.45rem;
		margin:0.4rem 0 !important;
	}
	a:not(.text-primary){
		color: rgba(#fff,0.8);

	}
}

.page-wrapper {
	background: #f7f7f7;
	min-height: calc(var(--vh, 1vh) * 100);
	@media(max-width:1024px){
		// background-image:url('~@/assets/images/pageBg4.jpg');
		// background-repeat: repeat;
		// background-size:0.1rem 0.1rem;
		background:#fff;
	}
	.absolute{
		@media(max-width:1024px){
			position:static;
		}
	}

	.l-main {
		padding-top: 1.71rem;
		padding-bottom: 1.6rem;
		min-height: calc(var(--vh, 1vh) * 100);
		box-sizing: border-box;
		@media(max-height:750px) and (min-width:1024px){
			padding-top:1.3rem;
		}
		@media(max-width:1024px){
			padding-top:1.65rem;
			padding-bottom: 0.3rem;
			min-height:calc(var(--vh, 1vh) * 100 - 1.4rem);
		}
	}

	.msg {
		margin-top: 0.5rem;
	}

	.wrapper {
		padding: 0.6rem;
		border-radius: 0.18rem;
		background: #fff;
		position:relative;
		@media(max-width:1024px){
			padding:0;
			border-radius:none;
			background:none;
		}
		&.job{
			&:after{
				content:'';
				display: block;
				position:absolute;
				right:0.4rem;
				bottom:0.4rem;
				width:3.3rem;
				height: 3.72rem;
				background:url('~@/assets/images/job-bg.png') no-repeat center center;
				background-size: cover;
				opacity:0.8;
				@media(max-width:1024px){
					display: none;
				}
			}
			.info-head{
				.position{
					font-size: 0.36rem;
					font-family: "PingFang-Heavy";
					// font-weight: 800;
					color: #2A2A2A;
					line-height: 0.5rem;
					position:relative;
					span{
						position:relative;
						z-index:10;
					}
					@media(max-width:1024px){
						font-size: 0.38rem;
						line-height: 0.54rem;
						&:after{
							content:'';
							position:absolute;
							width:100%;
							left:-0.5rem;
							top:50%;
							display: block;
							height: 0.76rem;
							margin-top: -0.38rem;
							background: linear-gradient(270deg, rgba(238, 238, 238, 0) 0%, rgba(216, 216, 216, 0.5) 100%);
						}
					}
				}
				.info-msg{
					font-size: 0.2rem;
					font-family: "PingFang Regular";
					font-weight: 400;
					color: #2A2A2A;
					line-height: 0.28rem;
					opacity:0.5;
					@media(max-width:1024px){
						display: none;
					}
				}
			}
			.info-cont{
				margin-top:0.18rem;
				@media(max-width:1024px){
					margin-top: 0.34rem;
				}
				.msg{
					margin-top:0.44rem;
					@media(max-width:1024px){
						margin-top: 0.56rem;
						&:first-child{
							margin-top: 0;
						}
					}
					.msg-tit{
						font-size: 0.24rem;
						font-family: "PingFang Regular";
						font-weight: bold;
						color: #2A2A2A;
						line-height: 0.32rem;
						position:relative;
						@media(max-width:1024px){
							font-size: 0.3rem;
							line-height: 0.42rem;

						}
					}
					.msg-info{
						font-size: 0.2rem;
						font-family: "PingFang Regular";
						font-weight: 400;
						margin-top:12px;
						color: #818181;
						line-height: 0.28rem;
						@media(max-width:1024px){
							font-size: 0.26rem;
							line-height: 0.37rem;
							margin-top: 0.1rem;
						}
					}
				}
			}
			.contact-email{
				margin-top:1.6rem;
				a{
					border-radius: 0.32rem;
					font-size: 0.2rem;
					font-family: "PingFang-Heavy";
					// font-weight: 400;
					color: var(--primary-color);
					line-height: 0.28rem;
					padding:0.11rem 0.32rem;
					display: inline-block;
					transition:all 0.25s;
					border: 1px solid rgba(#EB7D15,0.5);
				}
				a:hover{
					background: var(--primary-color);
					color:#fff;
					border-color:var(--primary-color);
				}
				@media(max-width:1024px){
					text-align: center;
					margin-top: 1rem;
					a{
						border-radius: 0.52rem;
						border: 0.02rem solid rgba(#EB7D15,0.5);
						font-size: 0.28rem;
						font-family: "PingFang-Heavy";
						text-align: center;
						display: inline-block;
						vertical-align: middle;
						font-weight: 800;
						color: #EB7D15;
						padding:0.2rem 0.7rem;
						line-height: 0.4rem;
						text-align: center;
					}
				}
			}
		}

		.title {
			font-size: 0.36rem;
			font-family: "PingFang-Heavy";
			// font-weight: 800;
			text-align: center;
			color: #2A2A2A;
			line-height: 0.5rem;
			padding-bottom: 0.2rem;
			border-bottom: 1px solid rgba(#E3E3E3,0.5);
			@media(max-width:1024px){
				font-size: 0.38rem;
				text-align:left;
				line-height: 0.54rem;

			}
		}

		.text-box {
			padding-top: 0.56rem;
			font-size: 0.2rem;
			font-family: "PingFang Regular";
			font-weight: 400;
			color: #818181;
			line-height: 0.28rem;

			p {
				margin-top: 0.2rem;

				&:first-child {
					margin-top: 0;
				}
			}
			@media(max-width:1024px){
				font-size: 0.24rem;
				padding-top:0.4rem;
				line-height:0.33rem;
			}
		}
	}
}

.collapse-transition{
	transition:all 0.5s ease;
}

.pc{
	display: block;
	@media(max-width:1024px){
		display: none;
	}
}
.mobile{
	display: none !important;
	@media(max-width:1024px){
		display: block !important;
	}
}
