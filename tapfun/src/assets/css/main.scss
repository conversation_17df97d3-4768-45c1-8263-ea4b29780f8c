// @import '../font/Poppins-Bold.css';
// @import '../font/Poppins-Regular.css';
@import './common.scss';

@keyframes spin {
    30% {
        transform: translate(-10px, 0px);
    }

    50% {
        transform: translate(0px, 10px);
    }

    80% {
        transform: translate(10px, 0px);
    }
    0%,
    100% {
        transform: translate(0px, -10px);
    }
}

@keyframes spin2 {
    30% {
        transform: translate(10px, 0px);
    }

    50% {
        transform: translate(0px, -10px);
    }

    80% {
        transform: translate(-10px, 0px);
    }

    /* 80% {
        transform: translate(0px, 0px);
    } */
    0%,
    100% {
        transform: translate(0px, 10px);
    }
}

.flex {
    display: flex;
    flex-wrap: wrap;
}

.fr {
    float: right;
}

.full-width {
    width: 100%;
}

.el-button--full {
    width: 100%;
}

// 表格tooltip样式
.el-tooltip__popper.is-dark {
    left: 20px;
}

//富文本编辑vue2-editor样式
.ql-container.ql-snow {
    max-height: 500px;
    overflow: scroll;
}

//iconfont
[class*=" ibnn-"],
[class^=ibnn-] {
    font-family: 'iconfont' !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: baseline;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

//menu icon style
.el-submenu [class^=ibnn-] {
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
    font-size: 18px;
}

//header style
.el-header {
    line-height: 60px;
    background-color: #484f54;
    font-size: 22px;
    color: #fff;

    .el-dropdown-link {
        cursor: pointer;
        color: #fff;
    }
}

//aside menu style
.el-aside {
    background-color: #545c64
}

.el-menu {
    border: 0;
    background-color: #545c64;
}

.el-submenu {
    .el-submenu__title {
        color: #fff;

        &:hover {
            background-color: #434a50;
        }
    }

    .el-menu-item {
        background-color: #434a50;
        color: #fff;
        padding-left: 50px !important;

        &.is-active {
            color: #ffd04b;
            // background-color: #676b65;
        }
    }

    .el-submenu {
        background-color: #434a50;

        .el-submenu__title {
            padding-left: 50px !important;
        }
    }

    i {
        color: #fff;
    }
}

.new-button {
    margin-bottom: 20px;
}

.pagination {
    text-align: right;
    margin-top: 20px
}

.red {
    color: red;
}

.pr0{
	padding-right: 0 !important;
}


//滚动条样式 只对webkit内核有效
// ::-webkit-scrollbar{width:8px;height:8px;}
// ::-webkit-scrollbar-button{display:none;}
// ::-webkit-scrollbar-track{display: none}
// ::-webkit-scrollbar-track-piece{background-color:rgba(0,0,0,0);}
// ::-webkit-scrollbar-thumb{margin-right:10px; background-color:rgba($color: #000000, $alpha: .6); border-radius: 4px;}
// ::-webkit-scrollbar-thumb:hover{background-color:#aaa;}
// ::-webkit-scrollbar-corner{background-color:#535353;}
// ::-webkit-scrollbar-resizer{background-color:#FF6E00;}
