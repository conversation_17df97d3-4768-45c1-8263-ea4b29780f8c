<template>
    <div class="full-width footer_bg l-inner sizing" :class="{'t-black':theme == 'black'}">
		<div class="flex justify-between horizontal items-center full-height">
			<div class="links">
				<appLink class="linkItem text-black" v-for="link in links" :to="link.url" :key="link.name" exact>
					{{link.name}}
				</appLink>
			</div>
			<p class="bottom-text">
				<a href="https://tsm.miit.gov.cn/dxxzsp/xkz/xkzgl/resource/qiyesearch.jsp?num=%25E9%2584%2582B2-20220115&type=xuke" target="_blank">
					《增值电信业务经营许可证》鄂B2-20220115
				</a>
				<a href="https://beian.miit.gov.cn/#/Integrated/index"  target="_blank">
					鄂ICP备2022002138号-1
				</a>
			</p>
		</div>
		<div class="flex items-center justify-center copyright">
			   <p> Copyright © 2018-2023 武汉创缘科技有限公司 版权所有</p>
		</div>
    </div>
</template>

<script>
import appLink from '@/components/Link.vue'
export default {
	props:{
		 theme:{
			 type:String,
			 default:'light'
		 }
	},
    components: { appLink },
    data() {
        return {
			links:[
				{name:'法律声明',url:'/statement'},
				// {name:'版权声明',url:''},
				{name:'隐私策略',url:'/privacyPolicy'},
				{name:'联系我们',url:'/contact'}
			]
        }
    }
}
</script>

<style lang="scss" scoped>
.horizontal{
	flex-direction: column;
}
.footer_bg {
	background: #F5F5F5;
	padding-top: 0.3rem;
	padding-bottom: 0.3rem;
	@media(max-height:800px){
		padding-top: 0.3rem;
		padding-bottom: 0.3rem;
	}
	&.t-black{
		background: #f7f7f7;
		.linkItem{

		}

	}
	@media(max-width:1300px){
		padding-top: .3rem;
		padding-bottom: .3rem;
		.flex{
			flex-direction: column;
		}
	}
	@media(max-width:1024px){
		padding-top: 0.3rem;
		padding-bottom: 0.3rem;
		.links{
			width:100%;
			text-align: center;
		}
		.copyright{
			width:100%;
			text-align: center !important;
			a{
				display: block;
			}
		}
	}
	.linkItem{
		font-size: 0.16rem;
		font-family: "PingFangSC-Regular";
		font-weight: 400;
		transition:color 0.3s ease;
		line-height: 0.28rem;
		color:#818181;
		@media(max-width:500px){
			font-size: .2rem;
			line-height: .3rem;
		}
		&:hover{
			color:var(--primary-color);
		}
		&:after{
			content:"";
			display: inline-block;
			vertical-align: middle;
			width: 1px;
			height: 0.14rem;
			margin:0 0.16rem;
			background: #ccc;
			@media(max-width:500px){
				height: .2rem;
			}
		}
		&:last-child{
			&:after{
				display: none;
			}
		}
	}
	.bottom-text{
		font-size: 0.14rem;
		font-family: "PingFangSC-Regular";
		text-align: right;
		color: #B4B4B4;
		line-height: 0.24rem;
		@media(max-width:500px){
			font-size: .18rem;
			line-height: .28rem;
		}
		a{
			color: #B4B4B4;
			margin:0 .1rem;
			@media(max-width:1400px){
				margin-left: .1rem;
			}
			@media(max-width:500px){
				display: block;
				text-align: center;
			}
			&:hover{
				color:var(--primary-color);
			}
		}
	}
	.copyright{
		font-size: 0.14rem;
		font-family: "PingFangSC-Regular";
		font-weight: 400;
		text-align: right;
		color: #B4B4B4;
		line-height: 0.24rem;
		@media(max-width:500px){
			font-size: .18rem;
			line-height: .28rem;
		}
		a{
			color: #818181;
			margin-left: .2rem;
			@media(max-width:1400px){
				margin-left: .1rem;
			}
			&:hover{
				color:var(--primary-color);
			}
		}
	}
}
</style>
