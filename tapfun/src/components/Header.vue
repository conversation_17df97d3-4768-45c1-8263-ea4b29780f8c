<template>
	<header class="header flex justify-between items-center window-width fixed left0 top0" :class="{'t-black':theme == 'black'}">
		<appLink class="logo" to="/?page=0">
			<img v-if="!showNav" :src="theme == 'black' ? require('@/assets/images/black-logo.png') : require('@/assets/images/logo.png')" alt="">
			<img v-else :src="require('@/assets/images/logo.png')" alt="">
		</appLink>

		<div class="heaer-right__nav pc pr40" >
			<div v-for="(link,linkIndex) in linkList" :key="linkIndex" class="dib relative slider-box">
				<a class="heaer-right__navItem fz18 lh25 text-white ml44 mr44 ffpf fw400"  @click="handlerClick(linkIndex)"  :class="{'text-primary':showSelected && currentIndex == linkIndex }" href="javascript:;">{{link.name}}</a>
				<div class="slider absolute" v-if="link.children.length && $route.name == 'home'">
					<div class="slider-item" v-for="(linkItem,productIndex) in link.children" :key="productIndex" @click="productClick(productIndex)">
						<img :src="linkItem.icon" alt="">
						<span>{{linkItem.productName}}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="heaer-right__nav mobile" @click="switchNav">
			<img v-if="!showNav" :src="theme == 'black' ? require('@/assets/images/rightNav-black.png') : require('@/assets/images/rightNav.png')" alt="">
			<img v-else :src="require('@/assets/images/close.png')" alt="">
		</div>
		<Transition >
			<div class="navBox" v-show="showNav">
				<a class="heaer-right__navItem fz18 lh25 text-white ml44 mr44 ffpf fw400" v-for="(link,linkIndex) in linkList" @click="handlerClick(linkIndex)" :key="linkIndex" :class="{'text-primary':showSelected && currentIndex == linkIndex }" href="javascript:;">{{link.name}}</a>
			</div>
		</Transition >
	</header>
</template>

<script>
	import appLink from '@/components/Link.vue'
	import Transition from '@/components/transition'
    export default {
		props:{
			checkIndex:{
				type:[Number,String],
				default:''
			},
			showSelected:{
				type:Boolean,
				default:false
			},
			theme:{
				type:String,
				default:'white'
			}
		},
		components:{
			appLink,
			Transition
		},
		watch:{
			checkIndex(index){
				this.currentIndex=index;
			}
		},
        data(){
			return {
				showNav:false,
				currentIndex:this.checkIndex,
				linkList: [
					{ name: '首页',children:[] },
					{ name: '产品中心', children:[
						{productName:'WOW',icon:require('@/assets/images/wow.png')}
					]},
					{ name: '关于我们', children:[]},
					{ name: '加入我们', children:[] },
				]
			}
		},
		methods:{
			switchNav(){
				this.showNav=!this.showNav;
			},
			productClick(index){
				this.$emit('productNavClick',index)
			},
			handlerClick(linkIndex){
				this.showNav=false;
				this.$emit('itemClick',linkIndex)
				console.log(linkIndex,'route')
				if(this.$route.name != 'home'){
					this.$router.push({
						name:'home',
						query:{
							page:linkIndex
						}
					})
				}
			}
		}
    }
</script>

<style lang="scss" scoped>
	.heaer-right__navItem{
		transition:color 0.3s;
		&:hover{
			color:var(--primary-color) !important;
		}
	}
.slider-box{
	.slider{
		min-width:2.1rem;
		position:absolute;
		left:50%;
		top:180%;
		padding:0.1rem;
		transform:translateX(-50%) rotateX(-45deg);
		background: #363636;
		opacity:0;
		transition:transform 0.5s ease,opacity 0.2s,visibility 0.3s;
		box-sizing:border-box;
		visibility: hidden;
		border-radius:0.18rem;
		transform-origin:top center;
		&:after{
			content:"";
			display: block;
			width:28px;
			height: 12px;
			background:url('~@/assets/images/slider-top.png') no-repeat;
			background-size: cover;
			position:absolute;
			left:50%;
			transform:translateX(-50%);
			top:-12px;
		}
		.slider-item{
			border-bottom:1px solid rgba(#fff,0.07);
			padding:0.14rem 0.2rem;
			transition:background 0.3s;
			cursor: pointer;
			&:last-child{
				border:none;
			}
			img{
				width:0.28rem;
				border-radius:0.05rem;
				display: inline-block;
				vertical-align: middle;
			}
			span{
				font-size: 0.18rem;
				font-family: "PingFang-Heavy";
				// font-weight: 800;
				color: #FFFFFF;
				margin-left:0.1rem;
				display: inline-block;
				transition:color 0.3s;
				vertical-align: middle;
				line-height: 0.26rem;
			}
			&:hover{
				// background-color:var(--primary-color);
				span{
					color:var(--primary-color);
				}
			}
		}
	}
	&:hover{
		.slider{
			transform:translateX(-50%) rotateX(0deg);
			opacity:1;
			visibility: visible;
		}
	}
}
.header{
	z-index:1000;
	transition:all 0.3s;
	.logo{
		position:relative;
		z-index:10;
		display: inline-block;
		img{
			width:3.06rem;
			@media(max-height:750px) and (min-width:1024px){
				width:2rem;
			}
		}
	}
	@media(max-width:1024px){
		// background-color:rgba(#000,0.2);
		background:linear-gradient(to bottom, rgba(#000,0.3),rgba(#000,0));
	}
	a{
		transition:color 0.3s;
		@media(max-width:1200px) and (min-width:1024px){
			margin:0 0.3rem;
		}
	}
	&.t-black{
		background:#fff;
		box-shadow: 0px 0.04rem 0.18rem 0px rgba(0, 0, 0, 0.04);
		.heaer-right__nav{
			.heaer-right__navItem:not(.text-primary){
				color:#2A2A2A;
			}
		}
	}
	.heaer-right__nav{
		@media(max-width:1024px){
			display: none;
		}
	}
	.pc{
		display: block;
		@media(max-width:1024px){
			display: none;
		}
	}
	.mobile{
		display: none;
		position:relative;
		z-index:10;
		img{
			width:1.12rem;
		}
		@media(max-width:1024px){
			display: block;
		}
	}
}
</style>
