<template>
    <component :is="type" v-bind="linkProps(to)" >
        <slot />
    </component>
</template>

<script>

export default {
    props: {
        to: {
            type: [String, Object],
            required: true
        }
    },
    computed: {
        isExternal() {
            return this.isExternalUrl(this.to)
        },
        type() {
            if (this.isExternal) {
                return 'a'
            }
            return 'router-link'
        }
    },
    methods: {
        isExternalUrl(path) {
            return /^(https?:|mailto:|tel:)/.test(path)
        },

        linkProps(to) {
            if (this.isExternal) {
                return {
                    href: to,
                    target: '_blank',
                    rel: 'noopener'
                }
            }
            return {
                to: to
            }
        }
    }
}
</script>
