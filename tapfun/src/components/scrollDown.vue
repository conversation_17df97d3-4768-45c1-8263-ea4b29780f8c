<template>
	<div class="scroll-down absolute full-width left0 bottom24 tac">
		<div class="mouseIco relative">
			<i></i>
		</div>
		<div class="scroll-text fz14 lh25 fw400 text-white mt5">
			<span class="dib">Scroll down the mous</span>
			<img  class="dib ml5" :src="require('@/assets/images/down.png')" alt="">
		</div>
	</div>
</template>

<script>
</script>

<style lang="scss" scoped>
	@keyframes mouse-dot {
	    from { opacity: 0; }
	    33% { opacity: 1; }
	    to { transform: translateY(0.75*18px); -webkit-transform: translateY(0.75*18px); opacity: 0; }
	}
	.scroll-down{
		z-index:20;
		@media(max-width:1024px){
			display: none;
		}
	}
	.mouseIco{
		width:0.75*40px;
		height: 0.75*63px;
		border:0.75*3px solid #fff;
		border-radius:20px;
		opacity:0.5;
		box-sizing: border-box;
		margin:0 auto;
		i{
			width:0.75*6px;
			height: 0.75*13px;
			border-radius:0.75*6px;
			position:absolute;
			left:50%;
			display: inline-block;
			margin-left:-0.75*3px;
			top:0.75*12px;
			background:#fff;
			animation:mouse-dot 1.5s cubic-bezier(0.23, 1, 0.32, 1) infinite;
		}

	}
	.scroll-text{
		font-family: "PingFang Regular";
		opacity:0.5;
	}
</style>
