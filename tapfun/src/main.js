// import Vue from 'vue'
import App from './App.vue'
import router from './router'
// import http from "@/utils/http";
import '@/assets/css/reset.scss'
import '@/assets/css/main.scss'
import Header from '@/components/Header'
import Footer from '@/components/Footer'


// 引入vue-amap
import VueAMap from 'vue-amap';
Vue.config.productionTip = false
Vue.use(VueAMap);
// Vue.use(http);

// 初始化vue-amap
VueAMap.initAMapApiLoader({
  // 高德的key
  key: '0b24d0f3e66fa14219204d0b3e98f595',
  // 插件集合
  plugin: ['AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType'],
  // 高德 sdk 版本，默认为 1.4.4
  v: '1.4.4'
});

Vue.component('HeaderCom',Header)
Vue.component('FooterCom',Footer)
new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
