let data=[
	{
		type:'技术类',
		list:[
			{
				position:'高级/资深服务器Java开发工程师',
				info:'5年以上·本科·武汉',
				id:1,
				duty:[
					'1、负责公司软件产品的需求、分析、设计及核心代码的编码工作；',
					'2、与产品经理沟通需求，参与具体的项目立项及计划工作，并根据概要设计的要求进行模块的详细设计与核心代码实现；',
					'3、负责项目的开发工作根据公司及项目要求编写相应的技术文档等；',
					'4、了解业界领先的技术方案，与同事共同交流提升团队技术水平； ',
					'5、把控相应的开发进度、风险评估，对团队管理有一定的认识并可高效的执行落地；'
				],
				demand:[
					'1、5年以上相关工作经验，本科及以上学历；有北上广深杭等一线城市互联网企业2年以上工作经验优先；',
					'2、具有扎实的Java编程基础（java基础深度），理解IO、多线程，对JVM的原理有一定的了解（熟悉Python可以作为加分项）；',
					'3、 熟练使用Java常用框架和工具，如 Spring boot 、Spring Cloud、SpringMVC、Struts、Hibernate、Mybatis等；',
					'4、对于复杂的业务形态能有自己成熟的建模思路，并通过UML完整的表达出来。',
					'5、熟悉mysql，了解数据库性能调优，熟悉事务、锁、并发等机制；',
					'6、熟悉设计模式、掌握分布式缓存、消息队列的使用，有大型分布式、高并发、高负载、高可用性系统设计开发经验者优先；',
					'7、对技术和工程质量有追求，有技术博客或者Github主页是加分项；'
				]
			},
			{
				position:'高级Java开发工程师',
				info:'5年以上·武汉',
				id:2,
				duty:[
					'1、负责公司软件产品的需求、分析、设计及核心代码的编码工作；',
					'2、与产品经理沟通需求、参与具体的项目立项及计划工作，并根据概要设计的要求进行模块的详细设计与核心代码实现；',
					'3、负责项目的开发工作根据公司及项目要求编写相应的技术文档、培训文档等；',
					'4、跟踪、收集和吸收本行业技术发展信息和产品信息，并实现与同事的交流与共享；',
					'5、把控整个项目团队的开发进度、风险评估，对于团队管理有自己的认识及理解并可高效的执行落地。'

				],
				demand:[
					'1、熟练掌握JAVA，J2EE，myBatis,JQUERY,SPRING,quarz,JAVASCRIPT，WEBSERVICE并了解动态代理，AOP等',
					'2、对熟悉B/S或C/S架构, J2EE架构有较好的掌握，了解三层架构技术,熟悉常用的设计模式；',
					'3、具备5年以上的产品研发和项目研发经验；',
					'4、具备详细设计文档的编写能力，有良好的编码规范；',
					'5、有过项目管理的经验者优先。'
				]
			},
			{
				position:'web前端开发工程师',
				info:'3-5年·本科·武汉',
				id:3,
				duty:[
					'1、负责桌面端Web前端页面的设计与开发。',
					'2、负责移动端H5页面的设计与开发。'
				],
				demand:[
					'1、3-5年以上前端开发相关经验，要求本科及以上学历。',
					'2、熟悉JavaScript, HTML, CSS等前端开发技术。',
					'3、熟悉JSP/Servlet等Java开发技术。',
					'4、熟悉移动端H5页面的开发。',
					'5、熟悉 Vue,jQuery, Bootstrap 等技术。'
				]
			},
			{
				position:'高级/资深 Android 开发工程师',
				info:'3-5年·本科·武汉',
				id:4,
				duty:[
					'1、负责 Android 平台产品的设计研发和 项目进度计划；',
					'2、梳理需求，完成业务的抽象和功能设计；参与技术选型和方案评审，并产出完整的，能落地的技术方案；',
					'3、参与 Code Review，负责提升各技术项目中的代码质量，设计质量和工程质量，提高 小组技术水平；'

				],
				demand:[
					'1、3-5年以上软件开发经验，本科及以上学历；有北上广深杭等一线城市互联网企业2年以上工作经验优先；',
					'2、有较好的架构思想，工程编码习惯，对技术和高质量的代码有追求；',
					'3、熟悉各版本 SDK，各种组件，网络通信，数据存储，UI布局，动画与控件的使用，熟悉 Android 性能优化，布局优化，内存优化；',
					'4、熟悉不同版本的新特性以及版本差异，对不同厂商的系统特性有一定了解；',
					'5、了解 Android 内存管理机制，了解 Android 底层的技术架构；',
					'6、良好的英文文档阅读能力是加分项。'
				]
			},
			{
				position:'高级ios开发工程师',
				info:' 5年以上·武汉',
				id:5,
				duty:[
					'1、参与移动端iOS应用的开发，负责相关模块及功能的开发工作',
					'2、参与讨论程序功能设计，独立完成功能开发，验证和修正测试中发现的问题',
					'3、学习和研究新技术以满足产品的需求，根据开发过程中的体验对产品提出改进建议'
				],
				demand:[
					'1、五年以上软件开发经验，能独立负责ios开发；',
					'2、掌握objective-C/Swift的编码技能，熟练掌握iPhone/iPad界面及交互开发，掌握iOS多线程技术；',
					'3、熟悉TCP/UDP/HTTP通信协议；',
					'4、熟悉动画及特效实现方案；',
					'5、具备分析问题、独立攻关、能够有效分配时间掌控开发进度的能力',
					'6、理解iOS底层原理；',
					'7、掌握单元测试，性能测试，异步测试；',
					'8、能够快速完成高质量的代码，注重代码封装，注重代码细节。'

				]
			},
			{
				position:'高级/资深测试工程师',
				info:'2年以上·本科·武汉',
				id:6,
				duty:[
					'1、编写测试计划：仔细阅读项目规格说明、设计文档等，充分掌握系统的性能、特点、业务流程等，保证产品测试工作的有条不紊进行；',
					'2、编写测试用例：按照测试流程、计划以及对产品特性的把握，沟通确认测试的范围、重点，考虑逻辑、数据完整性等要求，编写测试用例，设计测试用数据及预期结果，做好测试前的准备工作，确保测试目的达成；',
					'3、搭建测试环境：保证测试环境的独立和维护测试环境的更新，做好测试前的准备工作，确保测试环境的稳定和版本的正确；',
					'4、执行测试：根据测试计划及测试案例，执行测试，并根据产品特点及测试要求，实施冒烟测试、系统测试、回归测试等，及时发现软件缺陷，评估软件的特性与缺陷；',
					'5、进行BUG验证：根据测试结果，与开发部门反复沟通测试情况，督促开发部门解决问题，修正测试中发现的缺陷，完善软件功能；',
					'6、进行测试记录和相应文档编写， 编写测试报告和对测试结果分析；'

				],
				demand:[
					'1、计算机相关专业，本科及以上学历；有北上广深杭等一线城市互联网企业2年以上工作经验优先；',
					'2、有2年以上App测试经历 深刻理解软件测试的作用、思想、流程；',
					'3、 具备较强的问题分析能力、自主学习能力及沟通协调能力；',
					'4、认真严谨，良好的敬业精神、组织协调和承压能力；'
				]
			}
		]
	},
	{
		type:'产品类',
		list:[
			{
				position:'高级产品经理',
				info:'3年以上·本科·武汉',
				id:7,
				duty:[
					'1、负责海外产品规划，根据市场需求，竞争态势和资源状况，收集、分析用户需求，合理规划版本需求范围，深入把握产品核心价值，加强产品创新，确保产品功能满足海外用户的需求；',
					'2、定义产品功能，负责产品需求的管理，收集、提炼、整理客户需求，参与和指导编写产品需求文档；',
					'3、跟踪监控产品研发过程：及时了解产品的研发情况，跟踪监控产品研发的质量和进度，对产品测试进行管理，组织产品验收和发布；',
					'4、与开发、测试、运营、市场等多个部门进行充分沟通协调，保证版本功能的市场曝光与迭代高效。'
				],
				demand:[
					'1、重点大学本科以上学历，计算机、设计等相关专业优先；',
					'2、三年以上知名互联网公司工作经验，对娱乐社交产品有深刻认识，执行力强；',
					'3、一年以上海外社交产品经验；',
					'4、有良好的逻辑思维能力、沟通能力和文字表达能力；',
					'5、具备良好的格局观及团队合作精神；',
					'6、学习能力强，适应力强。'
				]
			},
			{
				position:'产品经理（工具类） ',
				info:'1年以上·本科·武汉',
				id:8,
				duty:[
					'1、负责参与移动APP工具类等产品方案设计；',
					'2、负责针对具体的业务场景及需求，开展用户需求分析、市场和竞品分析、产品功能的设计等工作；',
					'3、根据业务发展以及平台能力建设需要，提供完整产品规划和解决方案；',
					'4、协同团队内成员按时高质量迭代发布，包括对外平台资源对接，持续推进产品高质量和口碑的落地实施、优化、发展。'
					 
				],
				demand:[
					'1、本科以上学历，1年以上移动APP产品岗位经历，英文正常工作能力达标;',
					'2、工作积极主动，有极强的责任心和敬业精神，能在较高压力工作环境下保持激情并解决各种难题；',
					'3、熟悉移动APP研发流程，深刻理解工具类型APP国内外发展趋势和用户体验，具有出色的产品规划设计能力和规划经验；',
					'4、具备出色的表达能力和感染力，沟通协调经验丰富。'

				]
			},
			{
				position:'产品经理',
				info:'2年以上·武汉',
				id:9,
				duty:[
					'1、参与欧美市场海外娱乐类移动APP产品方案设计；',
					'2、负责针对具体的业务场景及需求，开展用户需求分析、产品功能的设计等工作，负责App端、控制台产品需求的撰写； ',
					'3、协同团队内成员按时高质量高效率的完成产品迭代；'
				],
				demand:[
					'1、2年以上相关岗位工作经验，有北上广深杭等一线城市互联网企业1年以上工作经验优先；',
					'2、熟悉移动APP研发流程，有用户思维和较强的逻辑与数据分析能力，有条理能抓住重点；',
					'3、工作积极主动，责任心强，抗压能力强；'
				]
			},
			{
				position:'游戏策划',
				info:'3年以上·本科·武汉',
				id:10,
				duty:[
					'1、根据游戏的核心玩法，对游戏内容进行细节设计；',
					'2、负责撰写游戏开发的策划文档、功能需求、美术风格的制定；',
					'3、负责与技术人员进行沟通需求，落实游戏设计具体需求的实施；',
					'4、负责游戏产品的开发及上线，并在上线后收集产品的线上反馈，分析游戏产品数据，并根据数据进行产品相应的优化；',
					'5、研究学习其他同类或不同类产品，提出新的创意、游戏交互方式，对目前游戏提出优化方案；',
					'6、收集产品反馈，关注游戏产品数据，同时不断改进产品方案。'
					 
				],
				demand:[
					'1、本科及以上学历，计算机、数学相关专业优先考虑；',
					'2、3年及以上游戏策划经验，有过海外游戏成功项目经验，策划过千万级别以上用户的海外游戏优先考虑；',
					'3、有较强的数据分析能力，能够根据游戏线上数据的情况分析出问题所在以及提供相应的优化策略；',
					'4、有过基础数值策划的经验，参与设计过成功线上产品的数值部分者优先考虑',
					'5、了解目前外海游戏市场上各种游戏的基本运作机制。',
					'6、严密的逻辑思维和较强的抗压能力；',
					'7、熟悉欧美游戏文化优先考虑。'
				]
			}
		]
	},
	{
		type:'设计类',
		list:[
			{
				position:'资深UI设计师',
				info:'3年以上·武汉',
				id:11,
				duty:[
					'1、负责产品的UI设计、运营设计与品牌设计，把控整体设计风格，并制定可执行的设计规范；',
					'2、配合产品经理，带领其它设计师完成产品设计方案的落地执行, 配合工程师团队完成产品的开发；', 
					'3、提升设计团队的职业素养和设计水平； '
				],
				demand:[
					'1、3年以上相关岗位工作经验，有北上广深杭等一线城市互联网企业2年以上工作经验优先；',
					'2、对UI/UX设计有深入研究和独到见解，具备良好设计能力 和 用户体验思维；',
					'3、有完整C端移动互联网产品整体设计方案落地经验；',
					'4、深度理解Web、iOS、Android端的官方设计原则和规范。'
				]
			},
			{
				position:'资深UI设计师',
				info:'5年以上·武汉',
				id:12,
				duty:[
					'1、负责产品的UI设计、运营设计与品牌设计；把控及创新整体风格，并制定可执行的设计规范；',
					'2、和产品负责人，产品经理一起构思创意，提供具备优质产品体验的整体解决方案；',
					'3、对产品数据的变化与用户满意度有极高的敏感度，能灵活度量产品的整体体验水平，提出改进建议；',
					'4、时刻关注并分析流行产品设计趋势，研究目标用户需求，体验诉求与审美倾向，并迅速优化现有产品。'
					 
				],
				demand:[
					'1、专科及以上学历，设计类相关专业；5年以上互联网或移动互联网设计经验；',
					'2、具备成功商业化产品的项目经验与整体设计方案落地经验；',
					'3、深度理解Web、iOS、Android端的官方设计原则和规范；',
					'4、对UI/UX设计有深入研究和独到见解，具备良好设计能力，有较强的色彩搭配及审美能力。',
					'投递请在简历中附上作品集链接或者将作品集发到*****（邮件主题标明投递人姓名）'

				]
			},
			{
				position:'UI 设计师 ',
				info:'2年以上·武汉',
				id:13,
				duty:[
					'1、完成公司APP产品相关视觉和UI需求；',
					'2、配合项目目标输出设计规范；',
					'3、设计预研方案的设计。'
				],
				demand:[
					'1、大专以上学历，2年以上页面视觉设计经验，有互联网设计经验者优先；',
					'2、具有良好创意构思能力，理解分析能力强，手绘能力良好者优先；',
					'3、精通视觉效果呈现，具备独立的视觉设计和良好的沟通能力；',
					'4、对交互，产品有一定的专业认识，良好的设计推动能力；',
					'5、熟练操作各种设计软件，如Photoshop、flash等；',
					'6、应聘该职位请提供以往作品。',
					'7、有设计过海外产品经验者优先'
				]
			}
		]
	},
	{
		type:'运营类',
		list:[
			{
				position:'阿拉伯语运营',
				info:'1年以上·武汉',
				id:14,
				duty:[
					'1、负责收集、整理海外渠道内容；',
					'2、负责沟通，洽谈，解答海外客户问题；',
					'3、负责制定，执行海外推广计划；',
					'4、负责海外相关线上，线下活动的执行。'
				],
				demand:[
					'1、1年以上工作经验，有较好的西班牙语听说读写能力；',
					'2、具有使用该地区社交软件的经验，对该地区的互联网情况有一定的了解；',
					'3、沟通表达能力较好，有团队精神；'
				]
			},
			{
				position:'实习生',
				info:'武汉',
				id:15,
				duty:[
					'1、负责用户资源的建设，包括资源采集及体热点和优质内容库搭建和维护；',
					'2、负责APP内的内容运营，包括日常运营，用户反馈治理，用户兴趣研究等；',
					'3、负责英文内容的修改与校正优化；'
				],
				demand:[
					'1、乐于学习新知识，工作细致负责，抗压能力强，具备良好的团队合作和沟通能力；',
					'2、了解海外移动互联网，英文正常工作能力达标。',
					'3、英语读写能力优秀者优先；'
				]
			},
			{
				position:'产品运营',
				info:'1年以上·武汉',
				id:16,
				duty:[
					'1、负责线上社交类产品的日常运营工作；',
					'2、对数据进行分析，并对现有产品进行本地化运营与优化；',
					'3、线上活动策划及执行，围绕用户需求，提升用户充值与付费率；',
					'4、 负责处理客服反馈的问题及管理工作；'
				],
				demand:[
					'1、 1年以上互联网产品运营经验；',
					'2、 有自己的想法，对数据敏感，执行力强；',
					'3、 对用户运营有一定经验，具备活动策划及文案撰写能力；',
					'4、 强烈的工作愿望和责任心，有良好的学习能力和团队合作精神;'

				]
			},
			{
				position:'运营专员',
				info:'2年以上·武汉',
				id:17,
				duty:[
					'1、有海外app运营或成功项目经验，或偏直播或语聊也可；',
					'2、负责小说用户资源的建设，包括资源采集及体热点和优质内容库搭建和维护，协助提升推荐算法效率；',
					'3、负责APP内的内容审核及用户运营，包括日常运营，用户反馈治理，用户兴趣研究等；',
					'4、根据目标进行任务拆解、梳理运营工作流程、制定有效的运营方案，并及时进行数据总结和复盘。'
				],
				demand:[
					'1、有两年年以上app内容领域或运营工作经历，对优质内容有敏锐的判断力，喜欢看小说或者漫画；',
					'2、有良好的数据分析能力，广泛的信息获取渠道，对推荐算法有一定理解者优先；',
					'3、有海外app项目运营经验，会运营facebook等；',
					'4、乐于学习新知识，工作细致负责，抗压能力强，具备良好的团队合作和沟通能力，能独立负责运营工作；',
					'5、会日语或英语语言优先。'
				]
			},
			{
				position:'海外广告优化师',
				info:'武汉',
				id:18,
				duty:[
					'1、负责公司产品的海外推广，开展在Facebook/Adwords等主流渠道广告平台上的海外广告投放工作；',
					'2、分析广告素材报告，理解目标市场的美术风格，把握目标市场的语言风格，为广告素材制作和文案设计提供有建设性意见；',
					'3、制定广告创意，负责广告素材的协调制作，并跟踪优化；',
					'4、通过对相关数据及竞争对手的分析，不断优化方案和素材，制定详细可执行的投放策略；',
					'5、对数据敏感, 善于分析数据并根据数据对广告创意不间断的进行优化；',
					'6、协同各部门对投放广告的数据进行整理和汇报；',
					'7、具备较强执行力，能承受较强的工作压力。'
				],
				demand:[
					'1、英语四级以上, 优秀的英语写作翻译能力, 听说熟练；',
					'2、可以独立完成广告文案的设计，并且对不同市场的本地语言风格有很强的把握能力；',
					'3、可以独立确定不同区域市场的美术素材风格，告知设计部门明确的素材需求；',
					'4、有较强的沟通和理解能力，具备较强的数据敏感度和分析能力，有独立分析和解决问题的能力。'

				]

			},
			{
				position:'西班牙语运营',
				info:' 1年以上·武汉',
				id:19,
				duty:[
					'1、负责收集、整理海外渠道内容；',
					'2、负责沟通，洽谈，解答海外客户问题；',
					'3、负责制定，执行海外推广计划；',
					'4、负责海外相关线上，线下活动的执行。'
				],
				demand:[
					'1、1年以上工作经验，有较好的西班牙语听说读写能力；',
					'2、具有使用该地区社交软件的经验，对该地区的互联网情况有一定的了解；',
					'3、沟通表达能力较好，有团队精神；'
				]
			}
		]
	}
]

export default data;
