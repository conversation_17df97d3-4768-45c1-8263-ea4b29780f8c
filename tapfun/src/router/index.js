// import Vue from 'vue'
// import Router from 'vue-router'
Vue.use(VueRouter)
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
const PrivacyPolicy = () => import('@/views/privacyPolicy.vue')
const Index = () => import('@/views/Home.vue')
const about = () => import('@/views/about.vue')
const contact = () => import('@/views/contact.vue')
const job = () => import('@/views/jobInfo.vue')
const statement = () => import('@/views/statement.vue')
const reply = () => import("@/views/reply.vue");




const routes = [
	{ path: "/", component: Index, name: "home" },
	{ path: "/about", component: about, name: "about" },
	{ path: "/privacyPolicy", component: PrivacyPolicy, name: "privacyPolicy" },
	{ path: "/contact", component: contact, name: "contact" },
	{ path: "/statement", component: statement, name: "statement" },
	{ path: "/job/:id", component: job, name: "job", props: true },
	{ path: "/reply", component: reply, name: "reply", props: true }
];
const router = new VueRouter({
    mode: 'history',
    routes,
	scrollBehavior (to, from, savedPosition) {
	  return { x: 0, y: 0 }
	}
})

router.beforeEach((to,from,next)=>{
	if(to.name == 'home'){
		document.body.classList.add('oh')
	}else{
		document.body.classList.remove('oh')
	}
	next();
})

export default router;
