import { Message } from 'element-ui'
import axios from 'axios'

let i = ''
if (true && /^(192|0|127|localhost|web.manga.test)/.test(location.hostname)) {
  i = 1
} else if (/^(author.istorynovel.com)/.test(location.hostname)) {
  i = 2
} else {
  i = 3
}

let isDev=3;

export const baseURL = isDev == 1
  ? 'http://api.manga.test/app/auhtor-service/' : isDev == 2 ? 'https://authorapi.istorynovel.com/'
    : 'https://authorapi.novelmanga.com/'


axios.defaults.baseURL = baseURL
axios.defaults.timeout = 10000

const toType = obj => {
    return {}.toString
        .call(obj)
        .match(/\s([a-zA-Z]+)/)[1]
        .toLowerCase()
}

const filterNull = o => {
    if (toType(o) !== 'object' || o.nofilter) return o
    for (var key in o) {
        if (o[key] === null) {
            delete o[key]
        }
        if (toType(o[key]) === 'string') {
            o[key] = o[key].trim()
            if (o[key].length === 0) {
                delete o[key]
            }
        }
    }
    return o
}

const http = {}

http.base = (type, path, params, headers, callback) => {
    const token = localStorage.getItem('token') || ''
    let config = {
        headers: Object.assign(
            {},
            headers,
            { Token: token },
            { 'Content-Type': 'application/json;charset=UTF-8' }
        ),
        url: path,
        method: type
    }
    if (['undefined', ''].includes(token)) {
        delete config.headers.Token
    }
    if (type !== 'get') {
        config['data'] = params
    } else {
        params = filterNull(params) || {}
        config['params'] = params
    }
    return new Promise((resolve, reject) => {
        axios(config).then(({ data }) => {
            if (data.code != 0) {
                message.error(data.msg)
                if ([1001002].includes(data.code)) {
                    localStorage.setItem('token', '')
                    localStorage.setItem('userName', '')
                    setTimeout(() => {
                        if (location.pathname != '/authorCenter') {
                            location.href = '/authorCenter'
                        }
                    }, 1500)
                }
                reject(data || { code: -1, msg: 'Network service exception, please try again later' })
            } else {
                resolve(data.data)
            }
            callback && callback(data) // 欢迎使用同步 $get.('/api',params，res=>{})
        })
            .catch((e) => {
                console.log('时间:' + new Date() + ',接口异常: 错误代码,接口地址:' + path);
                message.error('Network service exception, please try again later')
            })
    })
}

http.install = Vue => {
    ;['get', 'post', 'put', 'delete'].forEach(
        h =>
        (Vue.prototype['$' + h] = (url, params, headers, callback) =>
            http.base(h, url, params, headers, callback))
    )
}
export default http
