<template>
	<div id="fullPage">
		<HeaderCom showSelected :checkIndex="currentIndex" @productNavClick="productNavClick" @itemClick="handlerClick" :theme="currentIndex == 3 ? 'black' : 'white'" />
		<ScrollDown v-if="currentIndex != 3" />
		<div class="fp-nav">
			<ul>
				<li @click="goTo(index)" v-for="(item,index) in navLength" :key="item" :class="{'active':currentIndex == index}"><i></i></li>
			</ul>
		</div>

		<div class="fullpage-wrapper" @touchstart="touchstart" @touchmove="touchmove"  @touchend="touchend" @mousewheel="mouseWheel" @DOMMouseScroll="mouseWheel" :style="translateStyle">
			<div class="page full-width full-height relative" :class="{'active':currentIndex === 0}">
				<template v-if="!isMobile">
					<video v-show="videoIsLoad"  class="full-width full-height video webVideo" @loadeddata="showVideo" :src="require('@/assets/video/bannerVideo.mp4')" muted preload loop autoplay></video>
					<img :src="require('@/assets/images/videoFirst.jpg')" v-show="!videoIsLoad"  class="full-width full-height video webVideo" alt="">
				</template>
				<!-- <video id="video" class="full-width full-height video mobileVideo" :src="require('@/assets/video/mobileVideo.mp4')" muted preload loop autoplay></video> -->
				<div class="banner-swiper">
					<swiper ref="Swiper" :options="bannerSwiperOption">
						<swiper-slide  class="swiper-banner-item"  v-for="(item,typeIndex) in bannerList" :key="item.img">
							<div class="img" :style="`backgroundImage:url(${item})`"></div>
						</swiper-slide>
					</swiper>
				</div>
				<div class="page-text absolute">
					<div class="title fz90 h126 fw300 text-white lh126">创造  简单  快乐</div>
					<div class="desc mt20 fz26 fw400 text-white lh36 opacity49">
						“为用户创造价值”为使命，立足全球市场，致力于为<br>
						手机端用户提供有用、好用的互联网泛娱乐产品
					</div>
				</div>
			</div>

			<div class="page page2 full-width full-height relative" :class="{'active':currentIndex === 1}">
				<div class="wrapper l-inner pt130">
					<div class="page-title">
						产品中心<span>Product Center</span>
					</div>
					<div class="productActiveImg">
						<img v-for="(img,imgIndex) in productImgList" :key="img" :src="img" :class="{'active':swiperActiveIndex == imgIndex}"
						 alt="">
					</div>
					<div class="swiper relative">
						<div class="product-topBar">
							<div class="product-top--item" @click="productClick(item,index,$event)" v-for="(item,index) in productList" :class="{'active':index == swiperActiveIndex}" :key="item.productName" >
								<div class="product-top--item-inner">
									<img :src="item.logo" alt="">
									<div class="name" v-if="index == 1">看點<br />小說漫畫</div>
									<div class="name" v-else>{{ item.productName}}</div>
								</div>
							</div>
						</div>
						<swiper ref="mySwiper" :options="swiperOptions">
							<swiper-slide v-for="item in productList" class="relative slide-item" :key="item.productName">
								<div class="product-info">
									<div class="product-top">
										<img :src="item.logo" alt="">
										<div class="name">{{item.productName}}</div>
									</div>
									<div class="intro">
										<p v-for="text in item.intro" :key="text" v-html="text"></p>
									</div>
									<div class="download">
										<appLink :to="item.android">
											<img :src="require('@/assets/images/google.png')" alt="">
										</appLink>
										<appLink :to="item.ios">
											<img :src="require('@/assets/images/apple.png')" alt="">
										</appLink>
									</div>
								</div>
							</swiper-slide>
						</swiper>
						<!-- <div class="swiper-button-next"></div>
						<div class="swiper-button-prev"></div> -->
					</div>
				</div>
			</div>

			<div class="page page3 full-width full-height relative" :class="{'active':currentIndex === 2}">
				<div class="wrapper l-inner pt130">
					<div class="page-title">
						关于我们<span>About us</span>
					</div>
					<div class="company">
						<p>武汉创缘科技有限公司（简称：“创缘科技”）创立于2018年8月，公司以“为用户创造价值”为使命，致力于互联网文化创意产品出海研发与运营。创缘科技坚持自主创新，成功打造多款海外工具、社交等产品，获得了海外众多用户喜爱。创缘科技积极拓展产品形态，丰富产品品类，着力构建海外泛娱乐产品矩阵。</p>
						<p>未来，创缘科技将坚持精品创作，不忘初心，持续在互联网文化创意出海领域深耕，坚持为用户创造有价值的产品！</p>
					</div>
					<div class="list">
						<div class="list-item" v-for="(item,index) in message" :key="item.bgColor">
							<template v-if="index % 2 == 1">
								<div class="img">
									<div :style="`backgroundImage:url(${item.img})`"></div>
								</div>
								<div class="text-box" :style="`background-color:${item.bgColor}`">
									<div class="text-wrapper">
										{{item.text}}
									</div>
								</div>
							</template>
							<template v-else>
								<div class="text-box" :style="`background-color:${item.bgColor}`">
									<div class="text-wrapper">
										{{item.text}}
									</div>
								</div>
								<div class="img" >
									<div :style="`backgroundImage:url(${item.img})`"></div>
								</div>
							</template>
						</div>
					</div>
				</div>
			</div>

			<div class="page page4 full-width full-height relative" :class="{'active':currentIndex === 2}">
				<div class="wrapper pt130 l-inner">
					<div class="page-title white-bg">
						加入我们<span>join us</span>
					</div>
					<div class="joinList">
						<div class="join-top">
							<div class="typeList">
								<div class="type-item" @click="changePostData(item,typeIndex)" v-for="(item,typeIndex) in postTyleList" :class="{'active':typeIndex == postTypeCheckIndex}" :key="item.type">
									<span>{{item}}</span>
								</div>
							</div>
							<appLink class="more" to="https://www.zhipin.com/gongsi/0772ce97b116dbdd1nB62967GFY~.html">
								<span>点击查看更多职位</span>
								<img :src="require('@/assets/images/readmore.png')" alt="">
							</appLink>
						</div>
						<div class="join-top-mobile">
							<swiper ref="Swiper" :options="joinSwiperOptions">
								<swiper-slide  class="top-swiper"  @click.native="changePostData(item,typeIndex)" v-for="(item,typeIndex) in postTyleList" :class="{'active':typeIndex == postTypeCheckIndex}" :key="item.type">
									<span>{{item}}</span>
								</swiper-slide>
							</swiper>
						</div>
					</div>
					<div class="postList">
						<ul id="postList">
							<li v-for="(item,postIndex) in postListData" :key="postIndex" :title="item.title" @click="goToJobInfo(item)">
								<div class="box">
									<div class="left">
										<div class="postName">{{item.position}}</div>
										<div class="postInfo">{{item.info}}</div>
									</div>
									<div class="right">
										<span>详细</span>
										<img :src="require('@/assets/images/right-arrow.png')" alt="">
									</div>
								</div>
							</li>
						</ul>
					</div>
					<appLink class="more mobile" to="https://www.zhipin.com/gongsi/0772ce97b116dbdd1nB62967GFY~.html">
						<span>点击查看更多职位</span>
						<img :src="require('@/assets/images/readmore.png')" alt="">
					</appLink>
				</div>
				<FooterCom class="absolute left0 bottom0 homeFooter" />
			</div>
		</div>
	</div>
</template>

<script>
	let time = 700;
	import appLink from '@/components/Link'
	import ScrollDown from '@/components/scrollDown.vue'
	import {Swiper,SwiperSlide} from 'vue-awesome-swiper'
	import postData from '@/postData'
	import 'swiper/css/swiper.css'
	import {debounce} from '@/utils/util'
	let _this;

	export default {
		components: {
			appLink,
			ScrollDown,
			Swiper,
			SwiperSlide
		},
		data() {
			return {
				videoIsLoad:false,
				postTypeCheckIndex:0,  // 职位分类选中索引
				navLength: 4,
				lastHeight: 0,
				winHeight: 900,
				swiperActiveIndex: 0,
				currentIndex: 0,
				cloneIndex: 0,
				isDone: true,
				bannerList:[
					require('@/assets/images/video1.png'),
					require('@/assets/images/video2.png'),
					require('@/assets/images/video3.png')
				],
				productImgList: [
					require('@/assets/images/wow-img.png')
				],

				productList: [
					{
						productName: 'WOW',
						logo: require('@/assets/images/wow.png'),
						intro: [
							'WOW是一款畅聊社交APP，我们为全球用户打造一个轻松愉悦的社交平台，在这里，用户可以通过视频聊天、语音聊天、玩游戏等多种方式发现志同道合的朋友，丰富用户的业余时间和精神生活。现在注册，为您提供WOW一般的交流体验！'
						],
						android: 'https://play.google.com/store/apps/details?id=com.wow.chat.and',
						ios: 'https://apps.apple.com/us/app/wow-match-live-video-chats/id1579018518',
						productImg: require('@/assets/images/wow.png')
					}
				],
				swiperOptions: {
					effect : 'fade',
					// navigation: {
					// 	nextEl: ".swiper-button-next",
					// 	prevEl: ".swiper-button-prev",
					// },
					on: {
						slideChange(Swiper) {
							let activeIndex = this.activeIndex;
							_this.swiperActiveIndex = activeIndex
						}
					},
				},
				bannerSwiperOption:{
					effect : 'fade',
					loop:true,
					autoplay:true
				},
				joinSwiperOptions:{
					slidesPerView :3.3,
					spaceBetween:10
				},
				start:{
					startX:0,
					startY:0,
				},
				move:{
					startX:0,
					startY:0,
				},
				direction:null,
				isMove:false,
				isMobile:false
			}
		},
		computed: {

			mobileSwiper() {
				return this.$refs.Swiper.$swiper
			},
			swiper() {
				return this.$refs.mySwiper.$swiper
			},
			translateStyle() {
				return {
					transform: `translate3d(0,-${this.winHeight * this.cloneIndex}px,0)`
				}
			},
			postTyleList(){
				return postData.map(item=>item.type);
			},
			postListData(){
				return this.isMobile ? postData[this.postTypeCheckIndex].list.slice(0,3) : postData[this.postTypeCheckIndex].list;
			},
			message(){
				return this.isMobile ? [
					{text:'我们重视产品工程文化，产品上我们强调“任何花在需求分析上的时间都是值得的”，技术我们强调 “Code Review”。',bgColor:'#5D70A6',img:require('@/assets/images/a1.jpg')},
					// {text:'技术我们强调 “Code Review”，每次的复盘，是自我对话的强化，通过复盘校正方向，不断精进自己。',bgColor:'#738C95',img:require('@/assets/images/a2.jpg')},
					{text:'努力奋斗，超额回报。我们会给出业界中上等的薪资，招聘到工作勤奋，对职业发展有渴望的人才。',bgColor:'#7D4C38',img:require('@/assets/images/a4.jpg')},
					{text:'每日零食水果+ 加班晚餐报销/打车报销',bgColor:'#1F827B',img:require('@/assets/images/a3.jpg')},
				] : [
				{text:'我们重视产品工程文化，产品上我们强调“任何花在需求分析上的时间都是值得的”。',bgColor:'#5D70A6',img:require('@/assets/images/a1.jpg')},
				{text:'技术我们强调 “Code Review”，每次的复盘，是自我对话的强化，通过复盘校正方向，不断精进自己。',bgColor:'#738C95',img:require('@/assets/images/a2.jpg')},
				{text:'努力奋斗，超额回报。我们会给出业界中上等的薪资，招聘到工作勤奋，对职业发展有渴望的人才。',bgColor:'#7D4C38',img:require('@/assets/images/a4.jpg')},
				{text:'每日零食水果+ 加班晚餐报销/打车报销',bgColor:'#1F827B',img:require('@/assets/images/a3.jpg')},
				]
			}
		},
		mounted() {
			_this = this;
			this.$nextTick(() => { //页面渲染完，在执行
				// this.currentIndex = 2;
				this.computedWidth();
				window.addEventListener('resize', this.computedWidth)
				if(this.$route.query.page && this.$route.query.page >= 0 &&  this.$route.query.page < 4){
					this.goTo(this.$route.query.page);
				}
				this.isMobileBrowser();
			})
		},
		beforeDestroy() {
			window.removeEventListener('resize', this.computedWidth)
		},
		watch: {
			currentIndex(index) {
				if (this.swiperActiveIndex == null && index == 1) {
					this.swiperActiveIndex = 0;
				}
			}
		},
		methods: {
			isMobileBrowser(){
				if(!/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
					this.isMobile=false;
				}else{
					this.isMobile=true;
				}
				console.log(this.isMobile,'isMobile')
			},
			showVideo(){
				// console.log('showVideo')
				this.videoIsLoad=true;
			},
			slideToIndex(index){
				console.log(index,'index')
			},
			loadedmetadata(){
				console.log('load完成')
			},
			productClick(data,index,e){
				this.swiperActiveIndex=index;
				this.swiper.slideTo(index);
			},
			goToJobInfo(data){
				this.$router.push({
					name:'job',
					params:{
						id:data.id
					},
					query:{
						postTypeCheckIndex:this.postTypeCheckIndex
					}
				})
				console.log(data,'data')
			},
			changePostData(data,index){
				this.postTypeCheckIndex=index;
				this.mobileSwiper.slideTo(index)
			},
			handlerClick(index){
				this.goTo(index);
			},
			productNavClick(index){
				this.goTo(1);
				this.$nextTick(()=>{
					this.swiper.slideTo(index);
				})
			},
			goTo(index) {
				this.currentIndex = index;
				this.cloneIndex = index;
				let path = this.$router.history.current.path;
				this.$router.replace({
					path,
				    query: {
				        page:index
				    },
				});
			},
			computedWidth() {
				this.winHeight = document.documentElement.clientHeight || document.body.clientHeight;
				this.isMobileBrowser();
			},
			touchstart(e){
				e.stopPropagation();
				this.start.startX=e.targetTouches[0].pageX;
				this.start.startY=e.targetTouches[0].pageY;
			},
			touchmove(e){
				e.stopPropagation();
				this.isMove=true;
				this.move.startX=e.targetTouches[0].pageX;
				this.move.startY=e.targetTouches[0].pageY;
			},
			touchend(e){
				e.stopPropagation();
				// if(document.querySelector('#postList').contains(e.target)){
				// 	return;
				// }
				// console.log(window.getComputedStyle(e.target.parentNode).overflow,e.target,'target')
				if(!this.isMove) return;
				// console.log('move')
				let disX=this.move.startX - this.start.startX;
				let disY=this.move.startY - this.start.startY;
				if(this.move.startX == 0) return;
				if(Math.abs(disX) > Math.abs(disY)){
					return;
				}
				if( Math.abs(disY) < 3) return;
				this.direction = disY < 0 ? 0 : 1;
				this.mobileMove();
				this.isMove=false;
			},
			getDirection(e) {
				var direct = 0;
				e = e || window.event;
				if (e.wheelDelta) { //判断浏览器IE，谷歌滑轮事件
					if (e.wheelDelta > 0) { //当滑轮向上滚动时
						direct = 1;
					}
					if (e.wheelDelta < 0) { //当滑轮向下滚动时
						direct = 0;
					}
				} else if (e.detail) { //Firefox滑轮事件
					if (e.detail > 0) { //当滑轮向上滚动时
						direct = 0;
					}
					if (e.detail < 0) { //当滑轮向下滚动时
						direct = 1;
					}
				}
				return direct;
			},
			handlerMove(d){
				let dom = document.querySelectorAll('.fullpage-wrapper>div');

				if(!this.isDone){
					return;
				}
				if (this.currentIndex == 0 && d == 1 || this.currentIndex == dom.length -1 && d == 0) {
					return;
				}

				this.isDone = false;
				let num = Number(this.currentIndex);
				if (d > 0) {
					num = num - 1;
				} else {
					num = num + 1;
				}
				if (num >= dom.length - 1) {
					num = dom.length - 1;
				}

				this.cloneIndex = num;

				setTimeout(() => {
					this.currentIndex = num;

				}, time - 500)

				setTimeout(() => {
					this.isDone = true;

					let path = this.$router.history.current.path;
					console.log('zhixing')
					this.$router.replace({
						path,
					    query: {
					        page: this.currentIndex
					    },
					});
				}, time)
			},

			mobileMove(){
				let d = this.direction;
				this.handlerMove(d)

			},
			mouseWheel(e) {

				let d = this.getDirection(e);
				this.handlerMove(d);
			},
			mouseWheel:debounce(function(e){
				 let d = this.getDirection(e);
				 this.handlerMove(d);
			 },40)
		}
	}
</script>

<style scoped lang="scss">
	.homeFooter{
		background:#f5f5f5 !important;
	}
	.banner-swiper{
		position:absolute;
		left:0;
		top:0;
		width:100%;
		height:100%;
		display: none;
		@media(max-width:1024px){
			display: block;
		}
		::v-deep .swiper-banner-item,.swiper-wrapper,.swiper-container{
			width:100%;
			height:100%;
			.img{
				width:100%;
				height:100%;
				 background-size:cover;
				 background-position:center center;
			}
		}
	}
	.wrapper {
		@media(max-height:750px) and (min-width:1024px){
			padding-top:0.8rem;
		}
		@media(max-width:1024px){
			padding-top:1.65rem;
		}
		.swiper {
			.swiper-button-next,
			.swiper-button-prev {
				position: absolute;
				width: 0.48rem;
				height: 1.38rem;
				top: 50%;
				transform: translateY(-50%);
				cursor: pointer;

				&:after {
					display: none;
				}
				@media(max-width:1024px){
					display: none;
				}
			}

			.swiper-button-next {
				background: url('~@/assets/images/next.png') no-repeat center center;
				background-size: cover;
				right: -11.3%;
			}

			.swiper-button-prev {
				background: url('~@/assets/images/prev.png') no-repeat center center;
				background-size: cover;
				left: -11.3%;
			}
		}
	}

	.productActiveImg {
		position: absolute;
		right: 18.75%;
		bottom: 0;
		width: 40%;
		transition:opacity 0.3s ease;
		z-index:20;
		// opacity:0.4;
		@media(max-width:1024px){
			width:6.3rem;
			right:0;
			 opacity:0.7;
			bottom:-0.3rem;
			// bottom:-1.2rem;
		}
		@media(max-width:400px){
			width:5rem;
		}
		@media(min-width:1024px){
			&:hover{
				opacity:1;
			}
		}
		img {
			position: absolute;
			right: 0;
			bottom: 0;
			width: 100%;
			opacity: 0;
			transition: transform 0.5s cubic-bezier(0.445, 0.145, 0.355, 1), opacity 0.15s cubic-bezier(0.445, 0.145, 0.355, 1);
			transition-delay: 0.2s;
			transform: translateY(11%);


			&.active {
				opacity: 1;
				transform: translateY(0%);
			}
		}
	}

	.video {
		display: block;
		width:100%;
		height:100%;
		object-fit: cover;
		&.webVideo{
			@media(max-width:1024px){
				display: none;
			}
		}
		&.mobileVideo{
			display: none;
			@media(max-width:1024px){
				display: block;
			}
		}
	}

	.page2 {
		background-image:url('~@/assets/images/page2bg.png');
		background-repeat:repeat;
		background-size: 0.1rem 0.1rem;
	}
	.page3{
		background:url('~@/assets/images/about_bg.jpg') no-repeat center center;
		background-size: cover;
		.company{
			font-size: 0.16rem;
			line-height: 0.28rem;
			font-family: "PingFang Regular";
			font-weight: 400;
			color: #FFFFFF;
			margin-top: 0.1rem;
			@media(max-width:1024px){
				font-size:0.22rem;
				line-height: 0.26rem;
				margin-top:0.22rem;
			}
			p{
				margin-top:0.15rem;
			}
		}
		.list{
			display:flex;
			margin-top: 0.32rem;
			@media(max-width:1024px){
				display: block;
			}
			.list-item{
				flex:1;

				&:hover{
					.text-box{
						opacity:1;
					}
					.img div{
						transform:scale(1.03);
					}
				}
				.text-box{
					padding-bottom:76.66%;
					overflow: hidden;
					position:relative;
					opacity:0.4;
					transition:opacity 0.5s ease-in-out;
					@media(max-height:800px) and (min-width:1024px){
						padding-bottom:60%;
					}
					.text-wrapper{
						padding:0 11.5%;
						display:flex;
						position:absolute;
						left:0;
						top:0;
						height:100%;
						width:100%;
						box-sizing:border-box;
						display:flex;
						align-items: center;
						font-size: 0.16rem;
						font-family: "PingFang Regular";
						font-weight: 400;
						color: #FFFFFF;

						transition:opacity 0.5s ease;
						line-height: 0.25rem;
						&::after{
							content:"";
							width:0.5rem;
							height: 0.43rem;
							background:url('~@/assets/images/arrow-right.png') no-repeat center center;
							background-size:cover;
							position:absolute;
							left:0.1rem;
							top:0.1rem;
							@media(max-width:1600px){
								width:0.5*0.8rem;
								height: 0.5*0.8rem;
							}
						}
						&::before{
							content:"";
							width:0.5rem;
							height: 0.43rem;
							background:url('~@/assets/images/arrow-left.png') no-repeat center center;
							background-size:cover;
							position:absolute;
							right:0.1rem;
							bottom:0.1rem;
							@media(max-width:1600px){
								width:0.5*0.8rem;
								height: 0.5*0.8rem;
							}
						}
						@media(max-width:1200px){
							font-size:0.16rem;
							line-height: 0.22rem;
						}
						@media(max-width:1024px){
							font-size:0.2rem;
							padding:0 0.2rem;
							line-height:0.24rem;
						}
					}

				}
				.img{
					padding-bottom:76.66%;
					overflow: hidden;
					position:relative;
					@media(max-height:800px) and (min-width:1024px){
						padding-bottom:60%;
					}
					div{
						position:absolute;
						left:0;
						top:0;
						background-position:center center;
						background-size:cover;
						transition:transform 0.5s ease-in-out;
						width:100%;
						height:100%;
					}
				}
				@media(max-width:1024px){
					display: block;
					overflow: hidden;
					.text-box,.img{
						width:50%;
						float: left;
						opacity:1;
						padding-bottom:27.69%;
					}
					.text-box{
						.text-wrapper{
							&:after,&:before{
								width:0.5rem;
								height: 0.43rem;
							}
						}
					}
				}
			}
		}
	}
	.more{
		display:flex;
		align-items:center;
		justify-content: center;
		color:var(--primary-color);
		font-size: 0.2rem;
		font-family: "PingFang Regular";
		font-weight: 400;
		color: #EB7D15;
		text-align: center;
		line-height: 0.32rem;
		&.mobile{
			margin-top: 0.3rem;
		}
		@media(min-width:1024px){
			&:hover{
				img{
					transform:translateX(5px);
				}
			}
		}
		@media(max-width:1024px){
			margin-top:0.3rem;
			font-size: 0.24rem;
		}
		img{
			margin-left:0.07rem;
			width:0.18rem;
			display: inline-block;
			transition:transform 0.3s ease;
		}
	}
	.page4{
		background-image:url('~@/assets/images/page4bg.png');
		background-repeat:repeat;
		background-size:0.1rem 0.1rem;
		.wrapper{
			@media(max-width:1024px){
				padding-right: 0;
				.postList{
					padding-right:0.5rem;
				}
			}
		}
		.join-top-mobile{
			display: none;
			margin-top:0.36rem;
			@media(max-width:1024px){
				display: block;
			}
			.swiper-container{
				padding-bottom:0.1rem;
			}
			.top-swiper{
				font-size: 0.28rem;
				font-family: "PingFang-Heavy";
				font-weight: 800;
				color: rgba(#2A2A2A,0.3);
				text-align: center;
				line-height: 0.88rem;
				position:relative;
				padding-bottom:0.11rem;
				&:after{
					content:"";
					display: block;
					position:absolute;
					border-radius:0.2rem;
					width:100%;
					height: 0.88rem;
					left:0;
					top:0;
					background-color:rgba(#000,0.04);
				}
				span{
					position:relative;
					z-index:10;
				}
				&.active{
					&:after{
						background-image:url('~@/assets/images/jbg.png');
						background-size: 100% 100%;
						height:1rem;
						border-radius:0 !important;
						background-color:inherit !important;
					}
					span{
						color:#fff;
					}
				}
			}
		}
		.join-top{
			margin-top:0.6rem;
			display:flex;
			align-items:center;
			justify-content: space-between;
			@media(max-height:750px) and (min-width:1024px){
				margin-top:0.4rem;
			}
			@media(max-width:1024px){
				display: none;
			}
			.typeList{
				display:flex;
				@media(max-width:1024px){
					display: none;
				}
				.type-item{
					font-size: 0.28rem;
					font-family: "PingFang-Heavy";
					// font-weight: 800;
					color: #2A2A2A;
					margin-right: 1rem;
					line-height: 0.4rem;
					transition:opacity 0.3s;
					opacity:0.5;
					cursor: pointer;
					position:relative;
					@media(max-width:1300px){
						margin-right: 0.6rem;
					}
					@media(max-height:750px) and (min-width:1024px){
						font-size: 0.24rem;
					}
					span{
						position:relative;
						z-index:10;
					}
					&:last-child{
						margin-right:0;
					}

					&:after{
						content:'';
						display: block;
						width:0.4rem;
						height: 0.4rem;
						position:absolute;
						background:url('~@/assets/images/selected.png') no-repeat center center;
						background-size: cover;
						right:-0.15rem;
						top:-0.09rem;
						opacity:0;
						transition:all .3s ease;
						transform:translateY(5px);
					}
					&:hover{
						opacity:1;
					}
					&.active{
						opacity:1;
						font-weight: 800;
						&:after{
							opacity:1;
							transform:translateY(0px);
						}
					}
				}
			}


		}
		.postList{
			margin-top:35px;
			@media(max-width:1024px){
				margin-top:0.35rem;
			}
			ul{
				overflow: hidden;
				margin-left:-15px;
				margin-right:-15px;
				@media(max-width:1024px){
					margin-left:0;
					margin-right: 0;
					// max-height:5.5rem;
					overflow: auto;
				}
				li{
					width:33.33%;
					float:left;
					padding:0 15px;
					margin-bottom:30px;
					box-sizing:border-box;
					@media(max-width:1024px){
						width:100%;
						margin-bottom:0.2rem;
						padding:0;
					}
					.box{
						background: #F5F5F5;
						border-radius: 18px;
						height: 1.42rem;
						padding: 0 32px;
						cursor: pointer;
						display:flex;
						justify-content: center;
						// transition: background 0.3s ease,box-shadow 0.3s ease;
						flex-direction: column;
						@media(min-width:1024px){
							&:hover{
								background: #EB7D15;
								box-shadow: 0px 4px 18px 0px rgba(235, 125, 21, 0.43);
								.postName,.postInfo{
									color:#fff;
								}
							}
						}
						.postName{
							font-size: 0.28rem;
							font-family: "PingFang-Heavy";
							// font-weight: 800;
							color: #2A2A2A;
							// transition: color 0.3s ease;
							overflow: hidden;
							text-overflow:ellipsis;
							white-space:nowrap;
							line-height: 0.4rem;
						}
						.postInfo{
							font-size: 0.2rem;
							font-family: "PingFang Regular";
							font-weight: 400;
							opacity:0.7;
							margin-top: 0.14rem;
							// transition: color 0.3s ease;
							color: #2A2A2A;
							line-height: 0.28rem;
						}
						.right{
							display: none;
						}
						@media(max-height:750px) and (min-width:1024px){
							height: 1.1rem;
							.postName{
								font-size:0.24rem;
							}
							.postInfo{
								margin-top:0.1rem;
								font-size:0.16rem;
								line-height:0.24rem;
							}
						}
						@media(max-width:1300px) and (min-width:1024px){
							height: 1.2rem;
							padding:0 0.2rem;
							.postName{
								font-size:0.24rem;
							}
							.postInfo{
								margin-top:0.1rem;
								font-size:0.16rem;
								line-height:0.24rem;
							}
						}
						@media(max-width:1024px){
							height: 1.42rem;
							border-radius:0.18rem;
							padding:0 0.32rem;
							align-items:center;
							justify-content: space-between;
							flex-direction: row;
							.postName{
								font-size:0.32rem;
								line-height:0.44rem;
							}
							.postInfo{
								font-size:0.24rem;
								line-height:0.32rem;
								opacity:0.8;
							}
							.right{
								display: block;
								font-size: 0.24rem;
								font-family: "PingFang Regular";
								font-weight: 400;
								color: #2A2A2A;
								line-height: 0.32rem;
								span{
									display: inline-block;
									vertical-align: middle;
									opacity:0.8;
								}
								img{
									display: inline-block;
									vertical-align: middle;
									width:0.22rem;
									margin-left:0.08rem;
								}
							}

						}
					}
				}
			}
		}
	}

	.slide-item {
		.product-top,
		.intro,
		.download {
			transform: translateY(15px);
			opacity: 0 !important;
			transition: transform 0.38s cubic-bezier(0.445, 0.145, 0.355, 1), opacity 0.38s cubic-bezier(0.445, 0.145, 0.355, 1);
		}

		&.swiper-slide-active {
			.product-top,
			.intro,
			.download {
				transform: translateY(0px);
				opacity: 1 !important;
			}

			.product-top {
				transition-delay: 0.18s;
			}

			.intro {
				transition-delay: 0.3s;
			}

			.download {
				transition-delay: 0.5s;
			}
		}
	}

	#fullPage {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
		z-index:100;
		.fp-nav {
			position: absolute;
			top: 50%;
			right: 0.6rem;
			transform: translateY(-50%);
			z-index: 10;
			@media(max-width:1024px){
				right:0.2rem;
				display: none;
			}
			li {
				position: relative;

				i {
					content: '';
					display: block;
					width: 0.16rem;
					height: 0.16rem;
					border: 1px solid #aaa;
					box-sizing: border-box;
					border-radius: 50%;
					position: relative;
					cursor: pointer;
					transition: all 0.3s;
					background: #aaa;
				}

				&:after {
					content: '';
					display: block;
					width: 1px;
					height: 0.16rem;
					background: #aaa;
					transition: all 0.3s;
					opacity:0.4;
					margin: 0 auto;
				}

				&:before {
					content: '';
					display: block;
					width: 0.08rem;
					height: 0.08rem;
					background: #aaa;
					transition: all 0.3s;
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					top: 0.04rem;
					border-radius: 50%;
				}

				&.active {
					i {
						border-color: var(--primary-color);
						background: none;
					}

					&:before {
						background: var(--primary-color);
					}
				}

				&:last-child {
					&:after {
						display: none;
					}
				}
			}
		}

		.fullpage-wrapper {
			position: relative;
			width: 100%;
			height: 100%;
			transition: all 700ms ease 0s;
			font-family: "PingFang Regular";

			.page-text {
				left: 50%;
				top: 50%;
				width: 100%;
				text-align: center;
				transform: translate(-50%, -50%);
				font-family: "PingFang Regular";
				z-index:20;
				.title,
				.desc {
					position: relative;
					z-index: 10;
				}
				.title{
					font-family: "Akrobat-Black";
					@media(max-width:1024px){
						font-family: "Akrobat-Regular";
					}
				}
				&:after {
					content: '';
					position: absolute;
					left: 50%;
					top: 50%;
					width: 1266px;
					height: 360px;
					transform: translate(-50%, -50%);
					background: url('~@/assets/images/bg.png') no-repeat center center;
					@media(max-width:1024px){
						display: none;
					}

				}
			}


			.product-topBar{
				position:relative;
				display:none;
				margin-top:0.72rem;
				margin-left: -0.05rem;
				margin-right: -0.05rem;
				@media(max-width:1024px){
					display: flex;
				}
				.product-top--item{
					// flex:1;
					width: 2.1rem;
					.product-top--item-inner{
						margin:0 5px;
						padding:0.19rem;
						display:flex;
						align-items:center;
						position:relative;
						&:after{
							content:'';
							display: block;
							position:absolute;
							left:0;
							height:100%;
							width:100%;
							top:0;
							border-radius:0.2rem;
							background:rgba(#fff,0.04);
						}
					}
					&.active{
						img{
							opacity:1;
						}
						.name{
							color:#fff;
						}
						.product-top--item-inner{
							&:after{
								height:110%;
								border-radius:0;
								background:url('~@/assets/images/kbg.png') no-repeat;
								background-size:100% 100%;
							}
						}
					}
					img{
						width:0.5rem;
						opacity:0.3;
						transition:opacity 0.3s;
						position:relative;
						z-index:10;
						border-radius:0.1rem;
					}
					.name{
						font-size: 0.32rem;
						 font-family: "Akrobat-Black";
						font-weight: 800;
						color: rgba(#fff,0.3);
						line-height: 0.24rem;
						margin-left:0.13rem;
						position:relative;
						z-index:10;
						transition:color 0.3s;

					}
					&:nth-child(2) .name{
						font-size: 0.24rem;
					}
				}
			}
			.product-info {
				// margin-top: 1.06rem;
				margin-top: 8.83%;
				width: 55.833%;

				@media(max-width: 1500px) {
					width: 64%;
				}

				@media(max-width:1400px) {
					width: 70%;
					margin-top:5%;
				}
				@media(max-width:1024px){
					width:100%;
					margin-top: 0.35rem;
				}

				.product-top {
					display: flex;
					align-items: center;

					img {
						width: 0.9rem;
						@media(min-height:0px) and (min-width:1024px){
							width: 0.7rem;
						}
					}

					.name {
						margin-left: 0.3rem;
						font-size: 0.6rem;
						font-family: "Akrobat-Black";
						font-weight: 900;
						color: #FFFFFF;
						line-height: 1rem;
						@media(min-height:0px) and (min-width:1024px){
							font-size: 0.5rem;
						}
					}
					@media(max-width:1024px){
						display: none;
					}
				}

				.intro {
					min-height:2rem;
					// min-height: 1.5rem;
					// margin-top: 0.6rem;
					margin-top: 4.9%;
					font-size: 0.16rem;
					font-family: "PingFang Regular";
					font-weight: 400;
					color: rgba(#fff, 0.4);
					width:84%;
					line-height: 0.25rem;

					p {
						margin-top: 0.15rem;
					}
					@media(max-width:1400px) {
						margin-top: 5%;

					}
					@media(max-width:1024px){
						width: 100%;
						font-size:0.2rem;
						min-height:0;
						color: rgba(#fff, 0.7);
						line-height:0.26rem;
					}
				}

				.download {
					margin-top: 0.3rem;
					position:relative;
					z-index:100;
					@media(max-height:750px) and (min-width:1024px){
						margin-top:0.2rem;
					}
					@media(max-width:1024px){
						margin-top: 0.7rem;
					}
					a {
						margin-right: 0.16rem;

						img {
							width: 35.8%;
						}
						@media(max-width:1024px){
							display: block;
							margin-top:0.2rem;
							img{
								width:3.26rem;
							}
						}
					}
				}
			}
		}
	}
</style>
