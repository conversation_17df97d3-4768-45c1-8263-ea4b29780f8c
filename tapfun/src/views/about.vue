<template>
	<div class="page window-height">
		<HeaderCom />
		<div class="main sizing relative" >
			<div class="banner relative">
				<!-- <img :src="require('@/assets/images/about.png')" class="full-width" alt=""> -->
				<div class="title fz32 lh64 h64">關於我們</div>
			</div>
			<div class="w1260 tac fz18 lh48 fw400 content">
				<p>香港文洛華科技有限公司致力於移動互聯網技術研發及生產。</p>
				<p>公司擁有一大批來自美國矽谷各大型互聯網公司的優秀人才，以及在互聯網、軟件行業具有豐富經驗的管理人員，</p>
				<p>擁有一支專註於科技，精通互聯網和計算機軟件的技術團隊。</p>
				<p>公司以「打造精品APP」為理念，把」為用戶創造美好體驗」作為永恒的追求。</p>
				<p>公司將以持續的變革和創新精神，滿足不斷變化的用戶需求，為用戶和合作夥伴創造更多價值，共同開創移動互聯網和移動生活娛樂的新時代！</p>
			</div>
		</div>
		<FooterCom class="window-width sizing" />
	</div>
</template>

<script>
	import HeaderCom from '@/components/Header.vue'
	import FooterCom from '@/components/Footer.vue'
	export default{
		components:{
			HeaderCom,
			FooterCom
		},
		data(){
			return {
			}
		},
		methods:{

		}
	}
</script>

<style lang="scss" scoped>
.left_bg{
	position:absolute;
	left:0;
	top:0;
	width:55.55%;
	height: 100%;
	// background-image:url('~@/assets/images/bg.png');
	background-size:cover;
	background-position:center center;
}
.main{
	height:calc(calc(var(--vh, 1vh) * 100) - 1.54rem);
	position:relative;
	.title{
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #000000;
		border:2px solid var(--primary-color);
		padding:0 48px;
		position:absolute;
		display: inline-block;
		left:50%;
		transform:translateX(-50%);
		bottom:0.45rem;
	}
	.content{
		font-family: PingFangSC-Regular, PingFang SC;
		color: #1A1A1A;
	}
}

</style>
