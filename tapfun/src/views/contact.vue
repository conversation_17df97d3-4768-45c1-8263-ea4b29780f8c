<style lang="scss" scoped>
	.page-wrapper {

		.wrapper {
			border-radius: 0.18rem;
		}

		.amap-box {
			height: 5.74rem;
			border-radius:0.22rem;
			border:0.01rem solid #E7E7E7;
			::v-deep #amap-vue{
				border-radius:0.22rem;
			}

		}
		::v-deep .amap-icon{
			display: none;
			img{
				width:1.8rem;
			}
			@media(max-width:1024px){
				display: block;
			}
		}
		::v-deep .info-window{
			// background-color:var(--primary-color);
			background-color: #FF9632;
			border-radius:0.22rem;
			width:8.6rem;
			font-size: 0.22rem;
			font-family: "PingFang-Heavy";
			// font-weight: 800;
			color: #FFFFFF;
			line-height: 0.3rem;
			padding:0.2rem 0.3rem;
			display:flex;
			align-items:center;
			position:relative;
			@media(max-width:1024px){
				display: none;
			}
			&:after{
				content:"";
				display: block;
				width:80px;
				height: 30px;
				background:url('~@/assets/images/bot-img.png') no-repeat;
				position:absolute;
				left:50%;
				top:100%;
				transform:translateX(-50%);
			}
			.img{
				margin-right: 20px;
				background:url('~@/assets/images/cy.png') no-repeat;
				width:81px;
				height: 61px;
			}
			p{
				margin:5px 0;
			}

		}
		.contact-info{
			display: none;
			@media(max-width:1024px){
				display: block;
			}
		}
		.contactUs{
			display:flex;
			background: rgba(#000,0.02);
			border-radius: 0.22rem;
			margin-top: 0.25rem;
			align-items:center;
			padding:0.2rem 0.52rem;
			.logo{
				img{
					width:0.82rem;
				}
			}
			.contact_msg{
				margin-left: 0.18rem;
				font-size: 0.26rem;
				font-family: "PingFangSC-Regular";
				font-weight: 400;
				color: #818181;
				line-height: 0.36rem;
			}
		}
		.contact_email{
			border-radius: 0.52rem;
			border: 0.02rem solid rgba(#EB7D15,0.5);
			margin-top: 0.7rem;
			font-size: 0.28rem;
			font-family: "PingFang-Heavy";
			text-align: center;
			display: inline-block;
			vertical-align: middle;
			// font-weight: 800;
			color: #EB7D15;
			padding:0.2rem 0.7rem;
			line-height: 0.4rem;

		}
	}
</style>

<template>
	<div class="page-wrapper relative contact">
		<HeaderCom theme="black" />
		<div class="l-main l-inner">
			<div class="wrapper">
				<div class="title">联系我们</div>
				<div class="text-box">
					<el-amap class="amap-box" ref="map" :zoom="zoom" :center="center" :vid="'amap-vue'">
						<el-amap-info-window class="info-window" :position="currentWindow.position" :content="currentWindow.content" :visible="currentWindow.visible"
						 :isCustom="true" :offset="[-16,-64]" />

						 <el-amap-marker
								 :position="center"
								 :icon="icon"
								 :offset="offset"
						 ></el-amap-marker>
					</el-amap>

					<div class="contact-info">
						<div class="contactUs">
							<div class="logo">
								<img :src="require('@/assets/images/m-cy.png')" alt="">
							</div>
							<div class="contact_msg">
								办公地址：武汉光谷世贸中心现代光谷
								世贸中心4号门I栋1603/1604
							</div>
						</div>
						<div class="email tac">
							<a class="contact_email" href="mailto:<EMAIL>">联系邮箱：<EMAIL></a>
						</div>
					</div>

				</div>
			</div>
		</div>
		<FooterCom class="absolute bottom0 left0" theme="black" />
	</div>
</template>

<script>
	export default {
		data() {
			return {
				isMobile:false,
				zoom: 15,
				icon:require('@/assets/images/marker.png'),
				center: [114.421501, 30.472908],
				offset:[0,0],
				markerCenter:[114.419801, 30.473178],
				currentWindow: {
					position: [114.421501, 30.470908],
					content: '<div class="info-window"><div class="img"></div><div class="right"><p>办公地址：武汉光谷世贸中心现代光谷世贸中心4号门I栋1603/1604</p><p>联系邮箱：<EMAIL></p></div></div>',
					visible: true
				}
			}
		},
		created() {},
		mounted(){
			if(window.innerWidth < 1024){
				this.offset=[parseFloat(-390/750*100),parseFloat(-245/750*100)]
			}
		},
		methods: {}
	}
</script>
