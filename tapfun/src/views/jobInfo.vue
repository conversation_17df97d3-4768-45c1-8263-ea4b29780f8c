<style lang="scss" scoped>

</style>

<template>
	<div class="page-wrapper relative">
		<HeaderCom theme="black" />
		<div class="l-main l-inner">
			<div class="wrapper job">
				<div class="info">
					<div class="info-head flex items-center justify-between">
						<div class="position"><span>{{jobData.position}}</span></div>
						<div class="info-msg">{{jobData.info}}</div>
					</div>
					<div class="info-cont">
						<div class="msg">
							<div class="msg-tit">岗位职责：</div>
							<div class="msg-info">
								<p v-for="text in jobData.duty" :key="text">{{text}}</p>
							</div>
						</div>
						<div class="msg">
							<div class="msg-tit">岗位要求：</div>
							<div class="msg-info">
								<p v-for="text in jobData.demand" :key="text">{{text}}</p>
							</div>
						</div>
					</div>
					<div class="contact-email"><a href="mailto:<EMAIL>">投递邮箱：<EMAIL></a></div>
				</div>
			</div>
		</div>
		<FooterCom class="absolute bottom0 left0"  theme="black"  />
	</div>
</template>

<script>
	import postData from '@/postData'
	export default {
		props:{
			id:{
				type:[String,Number],
				required:true
			}
		},

		data() {
			return {
				jobData:{}
			}
		},
		created() {},
		mounted(){
			let index=this.$route.query.postTypeCheckIndex;
			let data=postData[index].list.find(item=>item.id == this.id);
			this.jobData=data;
		},
		methods: {}
	}
</script>
