<template>
<div class="myreply">
	<div class="reply">
    <div>
      <span class="label">用户名称：</span> <span class="l1">{{username}}</span>
    </div>
   <div>
      <span class="label">评论内容：</span> <span class="l2">{{comment}}</span>
    </div>
   <div>
      <span class="label">用户评分:</span> <span>{{score}}分</span>
    </div>
   <div>
      <span class="label">评论时间：</span> <span>{{time}}</span>
    </div>
   <div>
      <span class="label">应用名称：</span> <span>{{appName}}</span>
    </div>
   <div>
      <span class="label">版本信息：</span> <span>{{appVersion}}</span>
    </div>
   <div>
      <span class="label">设备信息：</span> <span>{{device}}</span>
    </div>
    <div class="reflex">
      <span class="label">回复信息：</span> <textarea v-model="replycontent"  ></textarea>
    </div>
    <button class="sub" @click="sub" v-show="show">发送</button>
    <!-- <div class="toast">发送成功</div> -->
  </div>
  </div>
</template>

<script>
import axios from 'axios'
	export default{
		data(){
			return {
        id:null,
        comment:'',
        replycontent:'',
        username:'',
        appName:'',
        device:'',
        appVersion:'',
        score:'',
        time:'',
        queryInfo:{
          reviewid:'',
          packagename:'',
        },
        show:true
			}
		},
    created () {
      this.queryInfo = {...this.$route.query}
      console.log(this.queryInfo);
      this.info()
    },
		methods:{
      info () {
        axios.get(`https://ad.novelmanga.com/comments/reply?reviewid=${this.queryInfo.reviewid}&packagename=${this.queryInfo.packagename}`).then(res=>{
          console.log(res);
            this.username = res.data.username
            this.appName = res.data.app_name
            this.score = res.data.score
            this.time = res.data.time
            this.device = res.data.device
            this.appVersion = res.data.app_version
            this.comment = res.data.comment
            this.replycontent = res.data.reply_content
            if (res.data.reply_content && res.data.reply_content !== '') {
              this.show = false
            } else {
              this.show = true
            }
        })
        
      },
      sub () {
        const formData = new FormData()
        formData.append('reviewid', this.queryInfo.reviewid)
        formData.append('packagename', this.queryInfo.packagename)
        formData.append('comment', this.queryInfo.comment)
        formData.append('replycontent', this.replycontent)
        axios.post('https://ad.novelmanga.com/comments/reply', formData).then((res) => {
          console.log(res);
          location.reload();
        })
      }
		}
	}
</script>

<style lang="scss" scoped>
.myreply{
  width: 100%;
  height: 100%;
  background: url('../assets/images/reply.png') no-repeat;
  background-size: cover;
}
.reply{
  width: 6.4rem;
  margin: auto;
  height: 600px;
  padding: .2rem .6rem;
  background: #FFFFFF;
  box-shadow: 0px 17px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 30px;
  border: 5px solid rgba(255, 255, 255, 0.35);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  font-size: 0.14rem;
  div{
    margin-top: 20px;
    display: flex;
    span{
      &:first-child{
        display: inline-block;
        width: 84px;
      }
      &:last-child{
        flex: 1;
      }
    }
  }
  .reflex{
    
  }
  textarea{
    padding: .1rem;
    line-height: 1.5;
    border: none;
    width: 80%;
    height: 1.8rem;
    background: #F6F6F6;
    border-radius: 10px;
  }
  .sub{
    padding:8px 12px;
    color: #fff;
    background-color: #000000;
    border: none;
    margin-left: 80px;
    margin-top: 20px;
    border-radius: 4px;
  }
  .label{
    color: rgba(51, 51, 51, 0.5);
  }
  .l1{
    font-size: 0.18rem;
    color: #222;
    font-weight: 600;
  }
  .l2{
    font-size: 0.14rem;
    background: #F6F6F6;
    border-radius: 10px;
    padding: 0.1rem 0.16rem;
    line-height: 1.5;
  }
  .toast{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    padding: 0.3rem 0.2rem;

  }
}

</style>
