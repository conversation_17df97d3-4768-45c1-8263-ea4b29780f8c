// vue.config.js
const CompressionPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg|otf|ttf)(\?.*)?$/i;

const path = require('path')

module.exports = {
    publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
    chainWebpack: (config)=>{
        config.resolve.alias//配置别名
            .set('@', path.resolve('src'))
            .set('assets',path.resolve('src/assets'))
            .set('components',path.resolve('src/components'))
            .set('views',path.resolve('src/views'))
    },
    configureWebpack:{
        externals: {
            'vue': 'Vue',
            'vue-router': 'VueRouter'
        },
        plugins: [
            new CompressionPlugin({
                filename: "[path].gz[query]",
                algorithm: "gzip",
                test: productionGzipExtensions,
                // 只处理大于xx字节 的文件，默认：0
                threshold: 10240,
                // 示例：一个1024b大小的文件，压缩后大小为768b，minRatio : 0.75
                minRatio: 0.6, // 默认: 0.8
                // 是否删除源文件，默认: false
                deleteOriginalAssets: false
            })
        ]
    },
    productionSourceMap:false,
    runtimeCompiler: true,
    indexPath: "index.html",
    assetsDir: 'static',
}
