
const register=require('@babel/register')
register({
    ignore:[/node_modules/],
    // presets:['@babel/preset-env','@babel/preset-react'],
    plugins:[
        '@babel/plugin-transform-modules-commonjs'
    ]
})


const {
    override,
    addDecoratorsLegacy,
    addWebpackAlias,
    addPostcssPlugins,
    addWebpackPlugin,
    fixBabelImports
} = require("customize-cra");
const path = require('path');
const CompressionPlugin = require("compression-webpack-plugin");

const WebpackMerge = require('webpack-merge');
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
const {
    BundleAnalyzerPlugin
} = require('webpack-bundle-analyzer');
const config = require("./src/utils/config");
let htmlConfig=config.default;

function resolve(dir) {
    return path.join(__dirname, '.', dir)
}
module.exports = override(
    addDecoratorsLegacy(),
    addWebpackAlias({
        '@': resolve("src")
    }),
    addPostcssPlugins([
        require('postcss-px-to-viewport')({
          viewportWidth: 1200,
          unitPrecision: 5,
          viewportUnit: 'vw'
        })
      ]),
    
    // fixBabelImports('import', {
    //     libraryName: 'antd',
    //     libraryDirectory: 'es',
    //     style: 'css',
    // }),
    //   addWebpackPlugin(
    //     new BundleAnalyzerPlugin({
    //       analyzerMode: 'static', //输出静态报告文件report.html，而不是启动一个web服务
    //     })
    //   ),

    config => {
        // 自定义配置
        config = WebpackMerge.merge(config, {});
        if (process.env.NODE_ENV === 'production') {
            // 生产模式下的配置
            config = WebpackMerge.merge(config, {
                output: {
                    publicPath: '../../', // 引用脚本相对路径
                },
                externals: {
                    'react': 'window.React',
                    'react-dom': 'window.ReactDOM'
                },
                plugins: [
                    new CompressionPlugin({
                        filename: "[path].gz[query]",
                        algorithm: "gzip",
                        test: productionGzipExtensions,
                        // 只处理大于xx字节 的文件，默认：0
                        threshold: 10240,
                        // 示例：一个1024b大小的文件，压缩后大小为768b，minRatio : 0.75
                        minRatio: 0.6, // 默认: 0.8
                        // 是否删除源文件，默认: false
                        deleteOriginalAssets: false
                    })
                ]
            });


            // console.log(hasHtmlPlugins.options,'hasHtmlPlugins')
            // 全局删除 console
            config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;

        } else {
            config = WebpackMerge.merge(config, {
                output: {
                    publicPath: './', // 引用脚本相对路径
                }
            })
        }

        let hasHtmlPlugins=config.plugins.find(
            plugin => plugin.constructor.name === 'HtmlWebpackPlugin'
        )
        if(hasHtmlPlugins){
            hasHtmlPlugins.options={
                ...hasHtmlPlugins.options,
                title:htmlConfig.title,
                headMeta:htmlConfig.headMeta
            }
        }
        // 返回更改后的配置
        return config;

    }
);
