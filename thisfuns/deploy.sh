#!/usr/bin/env bash

# file to upload
FOLDER_NAME="build/*"

#env, ssh remote, work dir  3wFGkZkW5k4X3Y3FdM9D
# ENV_PRO="pro"
# SSH_HOST_PRO="cy@**************"
# WORK_DIR_PRO="/home/<USER>/workspace/web/funstorys.com"
# RUM_PARAM_PRO=" --env pro "


# ENV_PRO="pro"
# SSH_HOST_PRO="cy@**************"
# WORK_DIR_PRO="/home/<USER>/workspace/web/shepherdnovels"
# RUM_PARAM_PRO=" --env pro "


# ENV_PRO="pro"
# SSH_HOST_PRO="cy@**************"
# WORK_DIR_PRO="/home/<USER>/workspace/web/ficfunapp"
# RUM_PARAM_PRO=" --env pro "


# ENV_PRO="pro"
# SSH_HOST_PRO="cy@*************"
# WORK_DIR_PRO="/home/<USER>/workspace/web/whatsreads.com"
# RUM_PARAM_PRO=" --env pro "

# ENV_PRO="pro"
# SSH_HOST_PRO="cy@*************"
# WORK_DIR_PRO="/home/<USER>/workspace/web/funtelnovel.com"
# RUM_PARAM_PRO=" --env pro "


ENV_PRO="pro"
SSH_HOST_PRO="cy@**************"
WORK_DIR_PRO="/home/<USER>/workspace/web/ereaderlab"
RUM_PARAM_PRO=" --env pro "



function upload() {
  echo $1
  echo $2
    # upload folder
    scp -r ${FOLDER_NAME} $1:$2
    ret=$?
    if [[ ${ret} -ne 0 ]] ; then
        echo 'Failed to scp folder'
        return 1
    fi
}

function deploy() {
    if [ "$1" = "${ENV_PRO}" ]; then
        SSH_HOST=${SSH_HOST_PRO}
        WORK_DIR=${WORK_DIR_PRO}
        RUN_PARAM=${RUM_PARAM_PRO}
    elif [ "$1" = "${ENV_TEST}" ]; then
        SSH_HOST=${SSH_HOST_TEST}
        WORK_DIR=${WORK_DIR_TEST}
        RUN_PARAM=${RUM_PARAM_TEST}
    else
        # TODO: local
        echo "Unknown env: $1"
        exit
    fi

    # upload jar and deploy.sh
    echo "*******Start to upload*******"
    upload ${SSH_HOST} ${WORK_DIR}
    ret=$?
    if [[ ${ret} -ne 0 ]] ; then
        echo 'Failed to upload files'
        exit ${ret}
    fi
}

if [ "$1" = "${ENV_TEST}" ]; then
    deploy $1
elif [ "$1" = "${ENV_PRO}" ]; then
    deploy $1
else
    echo "miss argument!!"
fi

