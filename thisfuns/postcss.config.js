module.exports = {
  plugins: {
    'postcss-px-to-viewport': {
      unitToConvert: ['px', 'rem','vh','vw'],  // 需要转换的单位
      viewportWidth: 1200,          // 设计稿宽度（根据你的 min-width:1200px）
      unitPrecision: 5,             // 转换精度
      viewportUnit: 'vw',           // 转换后的单位
      fontViewportUnit: 'vw',       // 字体转换单位
      selectorBlackList: [],        // 不转换的选择器
      minPixelValue: 1,             // 最小转换值
      mediaQuery: false             // 是否转换媒体查询
    },
    'postcss-preset-env': {}        // 支持现代CSS特性
  }
}