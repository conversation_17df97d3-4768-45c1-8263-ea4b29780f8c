<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
  

      <% if (htmlWebpackPlugin.options.headMeta && htmlWebpackPlugin.options.headMeta['appstore:developer_url']) { %>
        <meta name="appstore:developer_url" content="<%= htmlWebpackPlugin.options.headMeta['appstore:developer_url'] %>">
      <% } %>
      <% if (htmlWebpackPlugin.options.headMeta && htmlWebpackPlugin.options.headMeta['appstore:bundle_id']) { %>
        <meta name="appstore:bundle_id" content="<%= htmlWebpackPlugin.options.headMeta['appstore:bundle_id'] %>">
      <% } %>
      <% if (htmlWebpackPlugin.options.headMeta && htmlWebpackPlugin.options.headMeta['appstore:store_id']) { %>
      <meta name="appstore:store_id" content="<%= htmlWebpackPlugin.options.headMeta['appstore:store_id'] %>">
      <% } %>

    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->

      <title><%= htmlWebpackPlugin.options.title %></title>
    <script src="https://unpkg.com/react@17.0.2/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17.0.2/umd/react-dom.production.min.js"></script>
    <!-- <script src="https://unpkg.com/i18next@20.4.0/dist/umd/i18next.min.js"></script> -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
