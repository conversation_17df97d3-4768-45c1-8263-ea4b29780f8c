/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 * 0. sassCore's style
 */
html {
  /* font-family: sans-serif; */
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  /* overflow-y: scroll; */
  /* 0 */
  -webkit-overflow-scrolling: touch
  /* 0 */
}

/**
 * 1. Remove default margin
 * 0. sassCore's style.
 */
body {
  margin: 0;
  /* 1 */
  font-size: 12px;
  /* 0 */
  line-height: 2;
  /* 0 */
  color: #333;
  /* 0 */
  background-color: #fff
  /* 0 */
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11 and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 * 3. Correct `inline-block` display in IE 6/7.
 */
audio,
canvas,
progress,
video {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */
  *display: inline;
  /* 3 */
  *zoom: 1
  /* 3 */
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none;
}

/* Links
   ========================================================================== */
/**
 * 1. Remove the gray background color from active links in IE 10.
 * 2. Improve readability when focused and also mouse hovered in all browsers.
 * 0. sassCore's style.
 */
 .swiper-slide{
  min-height:0 !important;
}
 *{
  outline:none;
 }
a {
  background: transparent;
  /* 1 */
  /* 0 */
  text-decoration: none;
  color: #08c;
}
a:active, a:hover {
  outline: 0
  /* 2 */
}
a:hover {
  color: #006699;
}

/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic;
}

/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 8/9/10.
 * 2. Improve image quality when scaled in IE 7.
 * 0. sassCore's style.
 */
img {
  border: 0;
  /* 1 */
  vertical-align: middle;
  /* 0 */
  -ms-interpolation-mode: bicubic
  /* 2 */
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */
/**
 * Address differences between Firefox and other browsers.
 */
hr {
  box-sizing: content-box;
  height: 0;
}

/**
 * 1. Contain overflow in all browsers.
 * 2. Improve readability of pre-formatted text in all browsers.
 */
pre {
  overflow: auto;
  /* 1 */
  white-space: pre;
  /* 2 */
  white-space: pre-wrap;
  /* 2 */
  word-wrap: break-word
  /* 2 */
}

/**
 * 1. Address odd `em`-unit font size rendering in all browsers.
 * 2. Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  /* font-family: monospace, monospace; */
  /* 1 */
  /* _font-family: 'courier new', monospace; */
  /* 1 */
  font-size: 1em
  /* 2 */
}

/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0
  /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
  *overflow: visible
  /* 4 */
}

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

textarea:focus,
input:focus {
  outline: none;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal;
}

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
  *height: 13px;
  /* 3 */
  *width: 13px
  /* 3 */
}

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  /* 2 */
  box-sizing: content-box;
}

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */
  *margin-left: -7px
  /* 3 */
}

/**
 * 1. Remove default vertical scrollbar in IE 8/9/10/11.
 * 0. sassCore's style
 */
textarea {
  overflow: auto;
  /* 1 */
  resize: vertical
  /* 0 */
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}

/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/**
 * Address CSS quotes not supported in IE 6/7.
 */
q {
  quotes: none;
}

/* html,
button,
input,
select,
textarea {
  font-family: "Microsoft Yahei", "Helvetica Neue",Pingfang sc regular
, Helvetica, Tahoma, sans-serif;
} */

h1, h2, h3, h4, h5, h6, p, figure, form, blockquote {
  margin: 0;
}

ul, ol, li, dl, dd {
  margin: 0;
  padding: 0;
}

ul, ol {
  list-style: none outside none;
}

h1, h2, h3 {
  line-height: 2;
  font-weight: normal;
}

h1 {
  font-size: 18px;
}

h2 {
  font-size: 15.6px;
}

h3 {
  font-size: 14.04px;
}

h4 {
  font-size: 12px;
}

h5, h6 {
  font-size: 10.2px;
  text-transform: uppercase;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #ccc;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #ccc;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #ccc;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #ccc;
}

.clear {
  clear: both;
}

.f-cb {
  zoom: 1;
}

.f-cb:after {
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
  content: ".";
}

.fl {
  float: left;
  display: inline;
}

.fr {
  float: right;
  display: inline;
}

.por {
  position: relative;
}

.poa {
  position: absolute;
}

.pof {
  position: fixed;
}

.poa-f {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.ovh {
  overflow: hidden;
}

.noselect {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.z1 {
  z-index: 1;
}

.z2 {
  z-index: 2;
}

.z3 {
  z-index: 3;
}

.dn {
  display: none;
}

.dib {
  display: inline-block;
}

.w-f {
  width: 100%;
}

.h-f {
  height: 100%;
}

.fwn {
  font-weight: normal;
}

.tac {
  text-align: center;
}

.t-f {
  -webkit-transition: all 0.5s;
          transition: all 0.5s;
}

.t-c {
  -webkit-transition: color 0.5s;
          transition: color 0.5s;
}

.t-bg {
  -webkit-transition: background 0.5s;
          transition: background 0.5s;
}

.loader {
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  background: #f5f5f5;
}
.loader::after{
  content:'';
  display: block;
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -25px 0 0 -25px;
  font-size: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  -webkit-animation: spinner 700ms infinite linear;
          animation: spinner 700ms infinite linear;
}

.loader.white {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  border-left: 1px solid rgba(255, 255, 255, 0.5);
}

@-webkit-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

/*# sourceMappingURL=reset.css.map */
