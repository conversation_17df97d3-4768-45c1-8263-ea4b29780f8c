import React from 'react'
import './index.scss'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'

const langs = [{
    name: 'zh',
    text: '中'
}, {
    name: 'en',
    text: 'En'
}];

const Lang = () => {

    let { t, i18n } = useTranslation();
    console.log(i18n.language,'language')

    const changeLanguage = (lng) => {
        i18n.changeLanguage(lng);
    };

    return (
        <div className="lang flex items-center rad20 bg-white relative h36 w100">
            {
                langs.map((lang, index) => {
                    return <div className={classNames("flex1 tac fz16 lh22 fw400 ccc cursor lang-item", { 'primaryText': i18n.language === lang.name || i18n.language.includes(lang.name) })} onClick={() => changeLanguage(lang.name)} key={index}>{lang.text}</div>
                })
            }
        </div>
    )
}

export default Lang
