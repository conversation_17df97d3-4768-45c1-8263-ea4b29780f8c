<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Download Wowed</title>
  <style>
    html,
    body {
      width: 100%;
      height: 100%;
      background: #ff3f58;
      margin: 0;
      padding: 0;
    }

    .box {
      width: 100%;
      min-height: 100%;
      background: #ff3f58;

      /* background: url('./image/bg.png'), no-repeat;
      background-size: cover;
      background-position: center top; */
    }

    .btn {
      width: 100%;
      position: fixed;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0.36rem;
    }

    img {
      width: 100%;
    }
  </style>
</head>
<script>
  (function (doc, win) {
    var docEl = doc.documentElement,
      resizeEvt =
        "orientationchange" in window ? "orientationchange" : "resize",
      recalc = function () {
        var clientWidth = docEl.clientWidth;

        if (!clientWidth) return;

        docEl.style.fontSize = (clientWidth / 375) * 40 + "px";
      };

    if (!doc.addEventListener) return;

    win.addEventListener(resizeEvt, recalc, false);

    doc.addEventListener("DOMContentLoaded", recalc, false);
  })(document, window);

</script>
<body>
  <div class="box">
    <img src="./img/en_bg.png" alt="" id="img" />
    <div class="btn" id="btn" @click="downLoad">
      <img src="./img/en_btn.png" id="img1" alt="" />
    </div>
  </div>
</body>
<script src="./js/main.js"></script>
</html>
