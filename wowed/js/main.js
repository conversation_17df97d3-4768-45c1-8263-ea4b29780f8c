document.querySelector("#btn").addEventListener("click", () => {
 
    window.open('https://apps.samsung.com/appquery/appDetail.as?appId=com.wow.chat.pro.and');

});

function getUrlParams() {
  // 假设这里实现获取 URL 参数的逻辑
  // 示例：返回一个对象，其中键是参数名称，值是参数值
  const urlParams = new URLSearchParams(window.location.search);
  const params = {};
  urlParams.forEach((value, key) => {
    params[key] = value;
  });
  return params;
}

let param = "";
param = getUrlParams();

window.addEventListener("load", () => {
  let language = "en";
  if (navigator.languages && navigator.languages.length > 0) {
    // 对于支持 navigator.languages 的现代浏览器
    language = navigator.languages[0];
  } else if (navigator.language) {
    // 对于支持 navigator.language 的浏览器
    language = navigator.language;
  } else if (navigator.userLanguage) {
    // 对于 Internet Explorer (版本低于 11)
    language = navigator.userLanguage;
  }
  const img = document.querySelector("#img");
  const img1 = document.querySelector("#img1");
  const type = ["en", "es", "pt", "tr", "ae"].includes(language.toLowerCase());
  const language1 = type ? language.toLowerCase() : "en";
  img.src = `./img/${language1}_bg.png`;
  img1.src = `./img/${language1}_btn.png`;
});
