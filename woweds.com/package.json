{"name": "website", "version": "0.1.0", "private": true, "dependencies": {"@babel/plugin-proposal-decorators": "^7.17.9", "@babel/preset-react": "^7.16.7", "@babel/register": "^7.17.7", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "^4.16.13", "axios": "^0.27.2", "classnames": "^2.3.1", "compression-webpack-plugin": "^5.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "swiper": "^6.8.1", "web-vitals": "^1.0.1"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-plugin-import": "^1.13.3", "customize-cra": "^1.0.0", "node-sass": "^6.0.1", "react-app-rewired": "^2.1.8", "sass-loader": "^12.1.0", "webpack-bundle-analyzer": "^4.4.2", "webpack-merge": "^5.8.0"}}