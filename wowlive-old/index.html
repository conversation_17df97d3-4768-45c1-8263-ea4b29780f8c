<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="appstore:developer_url" content="https://apps.apple.com/hk/app/wow-match-live-video-chats/id1579018518" />
  <meta name="appstore:bundle_id" content="com.wow.chat.ios" />
  <meta name="appstore:store_id" content="1579018518" />
  <link rel="shortcut icon" href="./favicon.ico" />
  <title>WOW-Online Video call</title>
  <link rel="stylesheet" href="./css/index.css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/UAParser.js/0.7.20/ua-parser.min.js"
    integrity="sha512-70OZ+iUuudMmd7ikkUWcEogIw/+MIE17z17bboHhj56voxuy+OPB71GWef47xDEt5+MxFdrigC1tRDmuvIVrCA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://www.itxst.com/package/clipboardjs/clipboard.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js"
    integrity="sha512-a+SUDuwNzXDvz4XrIcXHuCf089/iJAoN4lmrXJg18XnduKK6YlDHNRalv4yd1N40OKI80tFidF+rqTFKGPoWFQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <style>
    @keyframes marquee {
      from {
        transform: translateY(100%);
      }

      to {
        transform: translateY(-100%);
      }
    }

    @keyframes marquee1 {
      from {
        transform: translateY(0%);
      }

      to {
        transform: translateY(-200%);
      }
    }

    @keyframes marquee2 {
      from {
        transform: translateY(-100%);
      }

      to {
        transform: translateY(100%);
      }
    }

    @keyframes marquee3 {
      from {
        transform: translateY(-200%);
      }

      to {
        transform: translateY(0%);
      }
    }

    .center .right .img1 {
      background-color: #fff;
      /* -webkit-animation: marquee 70s linear infinite; */
    }

    .center .right .img2 {
      /* -webkit-animation: marquee1 70s linear infinite;
      -webkit-animation-delay: -35s; */
    }

    .center .right .img3 {
      /* -webkit-animation: marquee2 64s linear infinite; */
    }

    .center .right .img4 {
      /* -webkit-animation: marquee3 64s linear infinite;
      -webkit-animation-delay: -32s; */
    }

    @keyframes marquee4 {
      from {
        transform: translateX(100%);
      }

      to {
        transform: translateX(-100%);
      }
    }

    @keyframes marquee5 {
      from {
        transform: translateX(0%);
      }

      to {
        transform: translateX(-200%);
      }
    }

    @keyframes marquee6 {
      from {
        transform: translateX(-100%);
      }

      to {
        transform: translateX(100%);
      }
    }

    @keyframes marquee7 {
      from {
        transform: translateX(-200%);
      }

      to {
        transform: translateX(0%);
      }
    }


    @media screen and (max-width: 1000px) {
      body {
        background: url("./imgs//bg1.png") no-repeat;
        background-size: cover;
        background-position: center top;
        width: 100vw;
        overflow: hidden;
      }

      header {
        display: none;
      }

      .center {
        overflow-x: auto;
        display: block;
        scrollbar-width: none;
        padding-bottom: 1rem;
      }

      &::-webkit-scrollbar {
        display: none;
      }

      .center .left {
        justify-content: flex-start;
        width: 100%;
        height: auto;
      }

      .center .left .meta img {
        display: block;
      }

      .center .left .title {
        margin-top: 0.5rem;
        font-size: 0.84rem;
      }

      .center .left .icon {
        display: block;
      }

      .center .left .p1 {
        display: none;
      }

      .center .left .p3 {
        display: none;
      }

      .center .left .download {
        display: none;
      }

      .center .left .text {
        display: none;
      }

      .center .right {
        width: 100%;
        display: none;
      }

      .footer {
        position: relative;
        font-size: 0.22rem;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
        margin-top: 0.5rem;
      }

      .center .right1 {
        display: block;
      }

      .center .download1 {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .center .text1 {
        display: flex;
      }

      #video {
        width: 100%;
        height: 100%;
        left: 0;
        border: none;
        border-radius: 0;
      }

      video {
        width: 100%;
        height: 100%;

        border: none;
        border-radius: 0;
      }

      #meta {
        margin-top: 0.24rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      #video_close {
        top: 0.22rem;
        right: 0.4rem;
        width: 0.52rem;
        height: 0.52rem;
      }

      #video_close img {
        width: 0.52rem;
        height: 0.52rem;
      }

      .footerText {
        display: block;
      }
    }





    .center .right1 .img1 {
      /* -webkit-animation: marquee4 70s linear infinite; */
    }

    .center .right1 .img2 {
      /* -webkit-animation: marquee5 70s linear infinite;
      -webkit-animation-delay: -35s; */
    }

    .center .right1 .img3 {
      /* -webkit-animation: marquee6 70s linear infinite; */
    }

    .center .right1 .img4 {
      /* -webkit-animation: marquee7 70s linear infinite;
      -webkit-animation-delay: -35s; */
    }
  </style>
</head>
<script>
  (function (doc, win) {
    var docEl = doc.documentElement,
      resizeEvt =
        "orientationchange" in window ? "orientationchange" : "resize",
      recalc = function () {
        var clientWidth = docEl.clientWidth;

        if (!clientWidth) return;

        docEl.style.fontSize = (clientWidth / 375) * 40 + "px";
      };

    if (!doc.addEventListener) return;

    win.addEventListener(resizeEvt, recalc, false);

    doc.addEventListener("DOMContentLoaded", recalc, false);
  })(document, window);
</script>

<body>
  <header>
    <div class="logo" id="logo">
      <img src="./imgs/logo.png" alt="" />
      <div>WOW-Online Video call</div>
    </div>
    <div class="right">
      <div class="item">
        <a class="text" href="mailto:<EMAIL>">Contact US</a>
      </div>
      <div class="item" id="privacy">
        <span class="text">Privacy Policy</span>
      </div>
      <div class="item" id="terms">
        <span class="text">Terms of Service</span>
      </div>
    </div>
  </header>
  <div class="center" id="center">
    <div class="left">
      <div class="meta" id="meta">
        <img src="./imgs/icon1.png" alt="" />
      </div>
      <div class="title">WOW</div>
      <p class="p3">WowPro is an application that allows you to chat in real-time</p>
      <p class="p3">with real people from around the world at any time.</p>
      <p class="p1">Video Chat to Make Friends</p>
      <div class="icon">
        <div class="p2">
          <p>Video Chat</p>
          <div class="icon-box">
            <img class="icon2" src="./imgs/icon2.png" alt="" />
            <img class="icon3" src="./imgs/icon3.png" alt="" />
          </div>
        </div>
        <p>to Make Friends</p>
      </div>
      <p>Enjoy your video chat now!</p>
      <div class="download">
        <!--  <img class="apple_store" id="apple_store" src="./imgs/apple_store.png" alt="" /> -->
        <img class="apk" id="apk" src="./imgs/apk.png" alt="" />
      </div>
      <!--  <div class="text" id="text">
        <span>View Tutorial</span>
        <img src="./imgs/icon.png" alt="" /></div> -->
    </div>
    <div class="right">
      <div class="item">
        <img class="img1" id="load1" src="./imgs/img1.png" alt="" />
        <img class="img2" id="load2" src="./imgs/img1.png" alt="" />
      </div>
      <div class="item">
        <img class="img3" id="load3" src="./imgs/img2.png" alt="" />
        <img class="img4" id="load4" src="./imgs/img2.png" alt="" />
      </div>
      <div class="item">
        <img class="img1" id="load1" src="./imgs/img3.png" alt="" />
        <img class="img2" id="load2" src="./imgs/img3.png" alt="" />
      </div>
      <div class="item">
        <img class="img3" id="load3" src="./imgs/img4.png" alt="" />
        <img class="img4" id="load4" src="./imgs/img4.png" alt="" />
      </div>
    </div>
    <div class="right1">
      <div>
        <div class="item">
          <img class="img1" id="load5" src="./imgs/img5.png" alt="" />
          <img class="img2" id="load6" src="./imgs/img5.png" alt="" />
        </div>
        <div class="item">
          <img class="img3" id="load7" src="./imgs/img6.png" alt="" />
          <img class="img4" id="load8" src="./imgs/img6.png" alt="" />
        </div>
      </div>
    </div>
    <div class="download1">
      <!-- <img class="apple_store" id="apple_store" src="./imgs/apple_store_1.png" alt="" /> -->
      <img class="apk" id="apk" src="./imgs/apk_1.png" alt="" />
    </div>
    <!-- <div class="text1" id="text1">
      <span>View Tutorial</span>
      <img src="./imgs/icon.png" alt="" />
    </div> -->
    <div class="footerText">
      <span>WowPro is an application that allows you to</span>
      <br>
      <span>chat in real-time with real people from</span>
      <br>
      <span>around the world at any time.</span>
    </div>
    <div class="footer">Copyright © 2024 wowed.live All Rights Reserved.</div>
  </div>

  <div class="make" id="make">
    <div class="close" id="close">
      <img src="./imgs/close.png" alt="" />
    </div>
    <div class="make-box">
      <div class="item home" id="home">
        <span class="text">Home</span>
      </div>
      <div class="item">
        <a class="text" href="mailto:<EMAIL>">Contact US</a>
      </div>
      <div class="item" id="privacy">
        <span class="text">Privacy Policy</span>
      </div>
      <div class="item" id="terms">
        <span class="text">Terms of Service</span>
      </div>
    </div>
  </div>
  <!-- <div class="video1" id="video">
    <video controls>
      <source src="./imgs/video.mp4" type="video/mp4" />
    </video>
    <div class="video_close" id="video_close">
      <img src="./imgs/close.png" alt="" />
    </div>
  </div> -->
  <script src="./js/main.js"></script>
</body>

</html>