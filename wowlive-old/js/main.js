!(function (f, b, e, v, n, t, s) {
  if (f.fbq) return;
  n = f.fbq = function () {
    n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
  };
  if (!f._fbq) f._fbq = n;
  n.push = n;
  n.loaded = !0;
  n.version = "2.0";
  n.queue = [];
  t = b.createElement(e);
  t.async = !0;
  t.src = v;
  s = b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t, s);
})(
  window,
  document,
  "script",
  "https://connect.facebook.net/en_US/fbevents.js"
);
fbq("init", "1214324646482785");
fbq("track", "ViewContent");

function getUrl() {
  // 假设这里实现获取 URL 参数的逻辑
  // 示例：返回一个对象，其中键是参数名称，值是参数值
  const urlParams = new URLSearchParams(window.location.search);
  const params = {};
  urlParams.forEach((value, key) => {
    params[key] = value;
  });
  return params;
}
let param = getUrl();
let Tools = {
  adPlatform: "Meta",
  eventSourceUrl: window.location.href,
  fingerprinting: {
    appChannel: "",
    platform: "",
    language: "",
    resolution: "",
    resolutionRatio: "",
    timeZone: "",
  },
  facebookEvent: {
    event_name: "StartTrial",
    event_time: getUtcTime(),
    event_source_url: window.location.href,
    user_data: {
      client_ip_address: "",
      client_user_agent: window.navigator.userAgent,
      fbc: "",
      fbp: "",
      country_code: "",
    },
    action_source: "website",
  },

  getUserAgent() {
    let navigator = window.navigator;
    let parser = new UAParser(navigator.userAgent);
    this.parseNavigator = parser.getResult();
    let language = navigator.language;
    this.fingerprinting.resolutionRatio = Number(
      window.devicePixelRatio
    ).toFixed(2);
    this.fingerprinting.resolution =
      window.screen.width * this.fingerprinting.resolutionRatio +
      ":" +
      window.screen.height * this.fingerprinting.resolutionRatio;
    this.fingerprinting.timeZone =
      Intl.DateTimeFormat().resolvedOptions().timeZone;
    let splitArr = language.split("-");
    if (splitArr.length === 3) {
      splitArr.splice(1, 1);
    }
    language = splitArr.join("-");
    this.fingerprinting.language = splitArr[0];
  },
  async init(query) {
    // let ua = window.navigator.userAgent || "";
    // let isAndroid = /android/i.test(ua);
    // let isIos = /iphone|ipad|ipod/i.test(ua);
    this.fingerprinting.platform = "Android";

    await this.getUserAgent();
    this.facebookEvent.user_data.fbc =
      query?.fbcid ||
      getCookie("_fbc") ||
      `fb.1.${+new Date()}.${query?.fbclid}`;
    this.facebookEvent.user_data.fbp = query?.fbpid || getCookie("_fbp");

    this.fingerprinting.appChannel = query?.appChannel;

    return this;
  },
};
Tools.init(param);
function getUtcTime() {
  var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
  var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
  var targetDate = new Date(nowDate + offset_GMT * 60 * 1000); //当前东八区的时间
  var current = Math.floor(targetDate.getTime() / 1000); //当前时区时间戳
  return current;
}

function getCookie(name) {
  let arr,
    reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
  if ((arr = document.cookie.match(reg))) return unescape(arr[2]);
  else return "";
}

const meta = document.querySelector("#meta");
const make = document.querySelector("#make");
const close1 = document.querySelector("#close");
const apple_store = document.querySelectorAll("#apple_store");
const apk = document.querySelectorAll("#apk");
const terms = document.querySelectorAll("#terms");
const privacy = document.querySelectorAll("#privacy");
const text = document.querySelector("#text");
// const text1 = document.querySelector("#text1");
// const video1 = document.querySelector("#video");
// const video_close = document.querySelector("#video_close");
const logo = document.querySelector("#logo");
const home = document.querySelector("#home");

meta.addEventListener("click", (e) => {
  e.preventDefault();
  make.style.display = "block";
});
close1.addEventListener("click", (e) => {
  e.preventDefault();
  make.style.display = "none";
});
// apple_store.forEach((item) => {
//   item.addEventListener("click", (e) => {
//     e.preventDefault();
//     window.open("https://apps.apple.com/app/id6504747245");
//   });
// });
apk.forEach((item) => {
  item.addEventListener("click",async (e) => {
    e.preventDefault();
    await downLoad("StartTrial");
    window.fbq("track", "StartTrial");
    // window.location.href = "https://oss-apk.woweds.com/Wow-4.7.5-9342-Wow-release.apk";
    if (param?.url) {
      setTimeout(() => {
        window.open(param.url);
      }, 1000);
    }
  });
});
privacy.forEach((item) => {
  item.addEventListener("click", (e) => {
    e.preventDefault();
    window.location.href = "https://wowed.live/wowlive/privacy.html"+window.location.search;
  });
});

logo.addEventListener("click", (e) => {
  e.preventDefault();
  window.location.href = "https://wowed.live/wowlive/index.html"+window.location.search;
});

terms.forEach((item) => {
  item.addEventListener("click", (e) => {
    e.preventDefault();
    window.location.href = "https://wowed.live/wowlive/service.html"+window.location.search;
  });
});
// text.addEventListener("click", (e) => {
//   e.preventDefault();
//   if (video1.style.display == "block") {
//     video1.style.display = "none";
//   } else {
//     video1.style.display = "block";
//   }
// });
// text1.addEventListener("click", (e) => {
//   e.preventDefault();
//   if (video1.style.display == "block") {
//     video1.style.display = "none";
//   } else {
//     video1.style.display = "block";
//   }
// });
// video_close.addEventListener("click", (e) => {
//   e.preventDefault();
//   if (video1.style.display == "block") {
//     video1.style.display = "none";
//   } else {
//     video1.style.display = "block";
//   }
// });
// home.addEventListener("click", (e) => {
//   make.style.display = "none";
// });

const load1 = document.querySelectorAll("#load1");
const load2 = document.querySelectorAll("#load2");
const load3 = document.querySelectorAll("#load3");
const load4 = document.querySelectorAll("#load4");
const load5 = document.querySelector("#load5");
const load6 = document.querySelector("#load6");
const load7 = document.querySelector("#load7");
const load8 = document.querySelector("#load8");
function encrypt(data, key = "com.novel.manga") {
  var key = CryptoJS.enc.Utf8.parse(getKey(key));
  let encrypted = CryptoJS.AES.encrypt(data, key, {
    iv: CryptoJS.enc.Utf8.parse("5873087691895047"),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}
function getKey(key) {
  return (key + "0000000000000000").substring(0, 16);
}
function copyTextToClipboard(input, { target = document.body } = {}) {
  let clipboard = new ClipboardJS("#apk", {
    text: function (trigger) {
      //返回字符串
      return input;
    },
  });
  clipboard.on("success", (e) => {
    clipboard.destroy();
  });
  clipboard.on("error", (e) => {
    clipboard.destroy();
  });
}
function downLoad(type) {
  const obj = {
    adPlatform: Tools.adPlatform,
    eventSourceUrl: Tools.eventSourceUrl,
    fingerprinting: Tools.fingerprinting,
    facebookEvent: Tools.facebookEvent,
  };
  obj.facebookEvent.event_name = type;
  
  const copy = encrypt(JSON.stringify(obj));
  copyTextToClipboard(copy);
  fetch(`https://wow.woweds.com/account_service/w2p/send`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(obj),
  })
    .then((response) => response.json())
    .then((data) => {})
    .catch(() => {
      localStorage.setItem("location", "US");
    });
}

window.addEventListener("load", () => {
  load1.forEach((item) => {
    item.style.animation = "marquee 70s linear infinite";
  });
  load2.forEach((item) => {
    item.style.animation = "marquee1 70s linear infinite";
    item.style.animationDelay = "-35s";
  });
  load3.forEach((item) => {
    item.style.animation = "marquee2 64s linear infinite";
  });
  load4.forEach((item) => {
    item.style.animation = "marquee3 64s linear infinite";
    item.style.animationDelay = "-32s";
  });
  load5.style.animation = "marquee4 70s linear infinite";
  load6.style.animation = "marquee5 70s linear infinite";
  load6.style.animationDelay = "-35s";
  load7.style.animation = "marquee6 70s linear infinite";
  load8.style.animation = "marquee7 70s linear infinite";
  load8.style.animationDelay = "-35s";
  downLoad("ViewContent");
});
