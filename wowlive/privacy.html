<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="shortcut icon" href="./favicon.ico" />
  <title>Privacy Policy</title>

  <script>
    (function (doc, win) {
      var docEl = doc.documentElement,
        resizeEvt =
          "orientationchange" in window ? "orientationchange" : "resize",
        recalc = function () {
          var clientWidth = docEl.clientWidth;

          if (!clientWidth) return;

          docEl.style.fontSize = 40 * (clientWidth / 375) + "px";
        };

      if (!doc.addEventListener) return;

      win.addEventListener(resizeEvt, recalc, false);

      doc.addEventListener("DOMContentLoaded", recalc, false);
    })(document, window);
  </script>
</head>
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: Roboto-Regular, Roboto;
    /* font-size: 0.24rem; */

    width: 100vw;

  }


  .copyright-notice {
    overflow: auto;
    height: 100vh;
    padding: 0 100px;
    background: url('./imgs/bg2.png') no-repeat;
    background-size: cover;
    background-position: center center;
    padding-bottom: 100px;
    /* Firefox */

  }

  .title {
    font-size: 32px;
    text-align: center;
    padding-top: 100px;


  }

  .para {
    font-size: 16px;
    color: #666666;
    line-height: 20px;
    padding: 10px 16px;

  }

  .nomarl {
    margin-left: 0.32rem;
    color: #666;
  }

  .isBig {
    color: #000;
    font-size: 0.28rem;
    margin-left: 0;
  }

  .isMost {
    font-size: 0.36rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #00000a;
    margin-left: 0;
  }



  .link {
    color: #08c;
    cursor: pointer;
  }

  .bold {
    font-weight: 700;
    color: #000;
  }

  .mgt-30 {
    margin-top: 0.3rem;
  }

  .link {
    color: #08c;
    cursor: pointer;
    text-decoration: underline;
  }

  .van-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  .van-dialog {
    width: 40vw;

    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;

    backface-visibility: hidden;
    transition: 0.3s;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    box-sizing: border-box;
    padding-top: 20px;


  }

  .header {
    font-size: 40px;
    padding-top: 26px;
    font-weight: 400;
    color: #00000a;
    line-height: 40px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;


  }

  .desc {
    text-align: center;
    font-size: 30px;
    font-weight: 400;
    color: #c3c3c3;
    line-height: 32px;
    margin-top: 20px;
    margin-bottom: 30px;


  }

  .content {
    width: 100%;
    margin-top: 20px;
    padding: 0 50px;
    box-sizing: border-box;


  }

  .van-field__body {
    display: block;
    height: 100px;
    width: 100%;
    display: flex;
    align-items: center;


  }

  .van-field__control {
    width: 100%;
    height: 60px;
    background: #fcfcfc;
    margin-top: 10px;
    border-radius: 15px;
    color: #818181;
    line-height: 32px;
    border: 1px solid #d6d6d6;
    padding: 20px 8px;


  }

  .textarea {
    color: #818181 !important;
    height: 150px;
    line-height: 40px;
    border: none;
    -webkit-font-smoothing: antialiased;


  }

  .textarea_body {
    height: 200px;
    background: #fcfcfc;
    position: relative;
    border: 1px solid #d6d6d6;
    border-radius: 15px;
    margin-top: 30px;
    display: flex;
    align-items: flex-start;


  }

  .num {
    position: absolute;
    right: 5%;
    bottom: 10%;
    font-size: 12px;
    color: #ccc;
  }

  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #ccc;
  }

  input:focus {
    border-color: #d6d6d6;
    /* 获得焦点时的边框颜色 */
    outline: none;
    /* 移除默认的轮廓线 */
    box-shadow: none;
    /* 添加阴影效果 */
  }

  textarea::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #ccc;
  }

  textarea:focus {
    border-color: #d6d6d6;
    /* 获得焦点时的边框颜色 */
    outline: none;
    /* 移除默认的轮廓线 */
    box-shadow: none;
    border: none;
    /* 添加阴影效果 */
  }

  .footer {
    width: 100%;
    display: flex;
    margin-top: 30px;


  }

  .buttom {
    width: 50%;
    text-align: center;
    height: 80px;
    line-height: 80px;
    border: 1px solid #d6d6d6;
    box-sizing: border-box;


  }

  .Cancel {
    color: #ababab !important;
    border-right: none;
  }

  .inputText,
  .textareaText {
    display: none;
    color: #ee0a24;
    font-size: 12px;
    text-align: left;
    margin-bottom: 20px;


  }

  .toast {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    font-size: 28px;


  }

  .toast p {
    width: 49%;
    text-align: center;
    color: rgba(255, 255, 255, .8);
    padding: 10px;
    border-radius: 10px;
    line-height: 35px;
    font-weight: 400;
    background-color: rgba(0, 0, 0, 0.7);



  }


  header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 0 54px;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    backdrop-filter: blur(32px);
    -webkit-backdrop-filter: blur(32px);
    z-index: 2;


  }

  .logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: #8524ff;
  }

  .logo img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .right {
    display: flex;
    align-items: center;
  }

  header .right .item {
    font-size: 16px;
    padding: 0 19px;
    color: #8524ff;
  }

  header .right .item .text {
    font-size: 16px;
    color: #8524ff;
    background: transparent;
    text-decoration: none;
    cursor: pointer;
  }

  header .right .item:nth-child(2n) {
    border-right: 1px solid rgba(155, 50, 254, 0.2);
    border-left: 1px solid rgba(155, 50, 254, 0.2);
  }


  &::-webkit-scrollbar {}


  .close {
    z-index: 10;
    display: none;
    padding-top: .2rem;

  }

  .close img {
    width: 0.52rem;
    height: 0.52rem;
  }


  @media screen and (max-width: 1000px) {
    .copyright-notice {
      padding: 0 .4rem;
      scrollbar-width: none;
      padding-bottom: 1rem;
    }

    .title {
      font-size: .32rem;
      padding-top: 0.3rem;
    }

    .para {
      font-size: .24rem;
      line-height: .45rem;
      padding: .10rem .16rem;
    }

    .van-dialog {
      width: 85vw;
      padding-top: .2rem;
    }

    .header {
      font-size: .4rem;
      padding: 0 .15rem;
      line-height: .4rem;
    }

    .desc {
      font-size: .3rem;
      line-height: 0.32rem;
      margin-top: 0.2rem;
      margin-bottom: .3rem;
    }

    .content {
      margin-top: 0.2rem;
      padding: 0 .5rem;
    }

    .van-field__body {
      height: 1rem;
    }

    .van-field__control {
      height: .6rem;
      margin-top: 0.1rem;
      border-radius: 0.15rem;
      line-height: 0.32rem;
      padding: 0.2rem 0.08rem;
    }

    .textarea {
      height: 1.5rem;
      line-height: .4rem;
    }

    .textarea_body {
      height: 2rem;
      border-radius: 0.15rem;
      margin-top: .3rem;
    }

    .footer {
      margin-top: .3rem;
    }

    .buttom {
      height: 1.2rem;
      line-height: 1.2rem;
    }

    .inputText,
    .textareaText {
      margin-bottom: 0.2rem;
    }

    .toast {
      font-size: .28rem;
    }

    .toast p {
      line-height: .35rem;
    }

    header {
      display: none;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    .close {
      display: flex;

      align-items: center;
    }
  }
</style>

<body>
  <!-- <div id="btn"> 12313245646</div> -->
  <header>
    <div class="logo" id="logo">
      <img src="./imgs/logo.png" alt="" />
      <div>WOW-Online Video call</div>
    </div>
    <div class="right">
      <div class="item">
        <a class="text" href="mailto:<EMAIL>">Contact US</a>
      </div>
      <div class="item" id="privacy">
        <span class="text">Privacy Policy</span>
      </div>
      <div class="item" id="terms">
        <span class="text">Terms of Service</span>
      </div>
    </div>
  </header>

  <div class="copyright-notice">
    <div class="close" id="close">
      <img src="./imgs/close.png" alt="" />
    </div>
    <div class="title">Privacy Policy</div>
    <p class="para mgt-20">
      We take the privacy and protection of personal information of our users
      very seriously. We understand the importance of personal information to
      you and will do our best to protect the security of your personal
      information. When you use our products or services, we may collect and
      use information about you. Through our Privacy Policy ("this Privacy
      Policy"), we want to explain to you how we collect, use, and protect
      this information when you use our products or services.
    </p>
    <p class="para mgt-20">
      We may offer different products or services, and if a separate product
      or service has a separate privacy policy, the privacy policy for that
      product and service will apply first. Anything not covered by that
      product and service's privacy policy will be governed by the contents of
      this privacy policy.
    </p>
    <p class="para mgt-20">
      This Privacy Policy does not apply to services provided to you by other
      independent third parties, for example, if a third party on our platform
      relies on our platform to provide services to you, the personal
      information you provide to the third party does not apply to this
      Privacy Policy and we are not responsible for any third party's use of
      the information provided by you.
    </p>
    <p class="para mgt-20">
      Please read and thoroughly understand this Privacy Policy before using
      our products or services, and use the relevant products or services
      after confirming that you fully understand and agree to it. If you do
      not agree to any of the contents of this Privacy Policy, you should
      immediately stop using our platform services. By using our products or
      services, you acknowledge that you have fully understood and agreed to
      the content of this policy.
    </p>
    <p class="bold para mgt-30">Part I. Definitions</p>
    <p class="para mgt-20">
      You: The term "you" in this Agreement refers specifically to the user of
      the product or service.
    </p>
    <p class="para mgt-20">
      Personal Information: Personal information for the purposes of this
      Agreement means any information recorded electronically or otherwise
      that, alone or in combination with other information, can identify a
      specific natural person or reflect the activities of a specific natural
      person. The personal information covered by this Privacy Policy
      includes: basic information (including personal birthday, gender, e-mail
      address); personal image screen; network identification information
      (including system account, IP address, e-mail address and password,
      password protection answer related to the foregoing); personal property
      information (transaction and consumption records and virtual property
      information); address book; personal Internet records (including website
      (including website browsing records, software usage records, click
      records); personal common device information (including hardware model,
      device MAC address, operating system type,software list unique device
      identification code (including IMEI/androidID/IDFA/OPENUDID/GUID, SIM
      card IMSI information, including information describing the basic
      situation of personal common devices).
    </p>

    <p class="bold para mgt-30">Part II Privacy Policy</p>
    <p class="para mgt-20">
      This policy will help you understand the following.
    </p>
    <p class="fw400 para mgt-10">
      I. How we collect your personal information
    </p>
    <p class="fw400 para mgt-10">II. how we use your personal information</p>
    <p class="fw400 para mgt-10">
      III. how we entrust the processing, sharing, transfer, public disclosure
      of your personal information
    </p>
    <p class="fw400 para mgt-10">
      IV. how we store and protect your personal information
    </p>
    <p class="fw400 para mgt-10">V. The use of cookies</p>
    <p class="fw400 para mgt-10">
      VI. Access, modification and deletion of information
    </p>
    <p class="fw400 para mgt-10">
      VII. How we handle personal information of minors
    </p>
    <p class="fw400 para mgt-10">VIII. Privacy Policy, Special Terms</p>
    <p class="fw400 para mgt-10">IX. Updates to the Privacy Policy</p>
    <p class="fw400 para mgt-10">X. How to contact us</p>

    <p class="bold para mgt-30">I. How we collect your information</p>
    <p class="para mgt-20">
      We collect and use your personal information in accordance with the
      principles of lawfulness, legitimacy and necessity, in order to achieve
      the following functions of this policy.
    </p>
    <p class="fw400 para mgt-20">(i) the information you provide to us.</p>
    <p class="para mgt-20">
      1. information you submit when you register for an account on our
      products or services
    </p>
    <p class="para mgt-20">
      When you register or log in to your account, you can create an account
      by e-mail, you can actively improve the relevant network identification
      information (such as avatar, nickname and password), you can fill in
      gender, birthday, impression tags and other information to improve your
      personal data.
    </p>
    <p class="para mgt-20">
      You can also use a third-party account (such as Google, Facebook, Apple)
      to log in to our products or services, at which time you will authorize
      us to obtain your public information on the third-party platform and
      bind your such third-party account to your account, so that you can log
      in and use our products and services directly through such third-party
      account. After completing registration, you will be able to use the
      corresponding functions on our products as a registered user. If you do
      not agree to authorize or deny us access to the above information, you
      may still enjoy the services on our products or services as a guest, but
      will not be able to enjoy the features and services of other registered
      users.
    </p>
    <p class="para mgt-20">
      When you use specific products or services (such as live streaming
      functions or services, payment and cash withdrawals, etc.), you are
      required to provide your real identity information (real name, ID
      number) according to relevant national laws and regulations. This
      information is sensitive personal information, and we will do our best
      to protect the security of your sensitive personal information. If you
      do not provide this information, you will not be able to use certain
      products or services, but it does not affect your enjoyment of the
      content browsing and registered users of the basic services.
    </p>

    <p class="para mgt-20">
      2. information you fill in or submit when using the relevant services we
      provide, including your gender, date of birth, email address, interests,
      relevant additional information (such as your province and city, postal
      code, etc.).
    </p>
    <p class="para mgt-20">
      Please note that several of our Services allow you to share information
      about yourself not only with your own social network, but also publicly
      with all users of the Service, for example, information you upload or
      post on our platform (including your publicly available personal
      information, lists you create), your responses to information uploaded
      or posted by others, and text and image information including those
      related to such information. Other users of our Services may also share
      information (including text and image information) about you. As long as
      you do not delete the shared information, the information will remain in
      the public domain; even if you delete the shared information, the
      information may still be independently cached, copied or stored by other
      users or unaffiliated third parties not under our control, or kept in
      the public domain by other users or third parties.
    </p>
    <p class="para mgt-20">
      Therefore, please consider carefully the content of the information you
      upload, post and exchange through our Services. In some cases, you may
      be able to control the range of users who are entitled to view the
      information you share through the privacy settings of some of our
      Services. To request the removal of your relevant information from our
      Services, please contact us.
    </p>

    <p class="fw400 para mgt-20">
      (ii) Information collected in the course of your use of the Services.
    </p>
    <p class="para mgt-20">
      In order to better serve you, we may collect information about you, such
      information includes.
    </p>
    <p class="para mgt-20">
      1. To ensure your normal use of our basic functions and normal services,
      to improve and optimize our service experience, and to protect your
      account security, we will automatically receive and record information
      about your browser, computer, mobile device, including but not limited
      to your IP address, type of browser, language used, date and time of
      access, hardware and software characteristics information and your web
      page records and other data; if you download or If you download or use
      our or our affiliates' client software, or access mobile web pages to
      use the services on our platform, we may read information related to
      your location and mobile device, including but not limited to device
      model, device identifier, operating system, resolution,
      telecommunications carrier, etc. Please understand that this information
      is essential information that we must collect to provide the Services
      and to ensure the proper functioning of the Services.
    </p>
    <p class="para mgt-20">
      2. We may collect content and information that you upload or post on
      your own through our Platform, such as text, shared photos, audio and
      video recordings and the date, time or location of such information.
      When you use these features, we will ask you to authorize camera, photo,
      and microphone permissions and device location information. You can
      choose whether to provide us with this authorization, and if you refuse,
      you will not be able to use this feature, but it will not affect your
      use of other features.
    </p>
    <p class="para mgt-20">
      3. In order to provide you with personalized content display and push,
      and improve user experience, we will collect the following information.
    </p>
    <p class="para mgt-20">
      (1) information about your operation and usage behavior such as
      attention, search and browsing records; for this purpose, we collect
      information about your device when you use our products or services,
      including device model, unique device identifier, operating system,
      resolution, telecommunication carrier software and hardware information.
    </p>
    <p class="para mgt-20">
      (2) Feedback, posting, likes, comments, rewards and other interactive
      information that you provide on your own initiative will be stored in
      our servers.
    </p>
    <p class="para mgt-20">
      (3) The geographic location information after obtaining your express
      authorization, this information allows us to recommend live content or
      dynamics of "nearby" users based on your geographic location, and enable
      your live content or dynamics to be presented on the "nearby" page.
      Refusal to provide information and permission about the location of your
      device may affect our ability to accurately locate your location and
      affect the "Nearby" function, but will not affect your normal use of
      other functions. In addition, you can also choose to turn on or off the
      corresponding display through the corresponding product's geolocation
      control switch.
    </p>
    <p class="para mgt-20">
      Location information is sensitive information, and refusal to provide
      this information will only prevent you from getting better live
      services, but will not affect your normal use of other functions.
    </p>

    <p class="para mgt-20">
      4. Provide top-up consumption-related functions or services
    </p>
    <p class="para mgt-20">
      When you use the consumption function of our products, we will collect
      your top-up records, consumption records information, so that you can
      check your own transaction records, while protecting your property,
      virtual property security to the maximum extent possible. The above
      information is sensitive information, but the collection of the above
      information is necessary to achieve the relevant functions, otherwise
      the transaction will not be completed.
    </p>
    <p class="para mgt-20">
      In addition to the above information, in order to improve the quality of
      our services, we may also collect other information from you, including
      and not limited to information you provide when you contact our customer
      service team, information you send to us in response to questionnaires
      you participate in, and information you provide when you interact with
      our affiliates and partners. At the same time, in order to improve the
      security of your use of the relevant services and to more accurately
      prevent phishing site fraud and Trojan horse viruses, we may determine
      the risk of your account by understanding some of your Internet usage
      habits, information about the software you commonly use, etc., and may
      record some URLs that we consider to be risky.
    </p>
    <p class="fw400 para mgt-20">
      (iii) Cases in which we obtain your personal information from third
      parties.
    </p>
    <p class="para mgt-20">
      In order to provide you with better services or to prevent Internet
      crimes, our affiliates and partners may share your personal information
      with us as required by law or with your consent. We may obtain account
      information (avatar, nickname) from third parties that you have
      authorized to share and, after you agree to this Privacy Policy, tie
      your third party account to your account so that you can log in and use
      our products or services directly through your third party account. We
      will use your personal information in accordance with the agreement with
      the third party, after confirming the legality of the source of personal
      information, and in compliance with relevant laws and regulations.
    </p>

    <p class="bold para mgt-30">II. How we use your information</p>
    <p class="para mgt-20">
      In order to improve the quality of our services, we may use your
      information for the following purposes.
    </p>
    <p class="para mgt-20">1. to provide services to you.</p>
    <p class="para mgt-20">
      2. for identity verification, customer service, security prevention,
      fraud monitoring, archiving and backup purposes, and to ensure the
      security of the products and services we provide to you.
    </p>
    <p class="para mgt-20">
      3. to recommend content that may be of interest to you, including, but
      not limited to, sending you information about products, services or
      related advertising (for example, sending commercial short messages to
      your email address about platform events, promotions, etc.), displaying
      personalized third-party promotions to you through the system or sharing
      information with our partners with your consent so that they can send
      you information about their products and services information about
      their products and services.
    </p>
    <p class="para mgt-20">
      4. we may combine your personal information with other service
      information for the purpose of using, sharing or disclosing it for the
      purpose of providing you with a more personalized use of the service,
      such as the need to give you a broader social circle.
    </p>
    <p class="para mgt-20">
      5. to allow you to participate in surveys, promotions and sweepstakes
      about our products and services.
    </p>
    <p class="para mgt-20">
      6. enabling us to better understand how you access and use our services
      so that we can respond to your individual needs in a targeted manner,
      such as language settings, location settings, personalized help services
      and instructions or otherwise respond to you and other users.
    </p>
    <p class="para mgt-20">
      7. software certification or management software upgrades.
    </p>
    <p class="para mgt-20">
      8. to involve you in surveys about our products and services.
    </p>
    <p class="para mgt-20">9. other uses as permitted by you.</p>

    <p class="bold para mgt-30">
      III. How we entrust the processing, sharing, transfer and public
      disclosure of your information
    </p>
    <p class="fw400 para mgt-20">(i) Entrusted processing</p>
    <p class="para mgt-20">
      Some specific modules or functions of the products or services we
      provide are provided by external suppliers. Companies, organizations and
      individuals to whom we entrust the processing of personal information
      will be subject to strict confidentiality agreements requiring them to
      handle personal information in accordance with our requirements, this
      Privacy Policy and any other relevant confidentiality and security
      measures.
    </p>

    <p class="fw400 para mgt-20">(ii) Sharing</p>
    <p class="para mgt-20">
      <span class="red">*</span> 1. Sharing with Affiliates: Your personal
      information may be shared with our affiliates and/or their designated
      service providers in order for us to provide you with products and
      services based on your account, to recommend information that may be of
      interest to you, to identify member account anomalies, and to protect
      the personal and property of our affiliates or other users or the public
      from harm. We will only share personal information that is necessary and
      subject to the purposes stated in this Privacy Policy, and will again
      seek your authorized consent if we share your personally sensitive
      information or if our affiliates change the purposes for which personal
      information is used and processed.
    </p>
    <p class="para mgt-20">
      <span class="red">*</span> 2. Sharing with Authorized Partners: We may
      engage authorized partners to provide certain services to you or perform
      functions on our behalf, and we will only share your information for the
      lawful, legitimate, necessary, specific, and explicit purposes stated in
      this Privacy Policy, and authorized partners will only have access to
      the information they need to perform their duties and will not use this
      information for any other purpose.
    </p>
    <p class="para mgt-20">
      Currently, our authorized partners include the following categories.
    </p>
    <p class="para mgt-20">
      (1) Authorized partners in the advertising and analytics services
      category. We do not share your personally identifiable information with
      partners who provide advertising and analytics services unless we have
      your permission to do so. We may provide these partners with information
      about the reach and effectiveness of their ads, but not your personally
      identifiable information, or we may aggregate this information so that
      it does not identify you personally. Such partners may combine the above
      information with other data that they have lawful access to for
      advertising or decision making recommendations.
    </p>
    <p class="para mgt-20">
      (2) Vendors, Service Providers and Other Partners. We send information
      to vendors, service providers and other partners who support our
      business by providing technical infrastructure services, analyzing how
      our services are used, measuring the effectiveness of advertisements and
      services, providing customer service, facilitating payments or
      conducting academic research and surveys.
    </p>
    <p class="para mgt-20">
      We will agree to strict data protection measures with them so that they
      handle personal information in accordance with our instructions, this
      Privacy Policy and any other relevant confidentiality and security
      measures.
    </p>

    <p class="fw400 para mgt-20">(iii) Transfer</p>
    <p class="para mgt-20">
      We will not transfer your personal information to any company,
      organization or individual, except for the following.
    </p>
    <p class="para mgt-20">
      <span class="red">*</span> 1. transferring with obtaining explicit
      consent: we will transfer your personal information to other parties
      after obtaining your explicit consent.
    </p>
    <p class="para mgt-20">
      <span class="red">*</span> 2. to satisfy laws and regulations, legal
      process requirements or mandatory government requests or judicial
      decisions.
    </p>
    <p class="para mgt-20">
      <span class="red">*</span> 3. if we or our affiliates are involved in a
      transaction such as a merger, separation, liquidation, acquisition or
      sale of assets or business, and your personal information may be
      transferred as part of such transaction, we will ensure the
      confidentiality of such information at the time of the transfer and
      require the new company or organization holding your personal
      information to continue to be bound by this privacy policy, or we will
      require the company or organization to re-submit to you to seek
      authorized consent.
    </p>
    <p class="fw400 para mgt-20">(iv) Public Disclosure</p>
    <p class="para mgt-20">
      We will only disclose your personal information publicly if we
    </p>
    <p class="para mgt-20">
      <span class="red">*</span>1. with your express consent or based on your
      voluntary choice, we may disclose your personal information publicly.
    </p>
    <p class="para mgt-20">
      <span class="red">*</span> 2. If we determine that you have violated
      laws and regulations or seriously violated the relevant agreements and
      rules of our products or services, or to protect the personal and
      property safety of other users or the public from infringement, we may
      disclose personal information about you in accordance with laws and
      regulations or with your consent, including the relevant violations and
      the measures we have taken against you.
    </p>

    <p class="bold para mgt-30">
      IV. How we keep and protect your personal information
    </p>
    <p class="fw400 para mgt-20">
      (i) The preservation of your personal information
    </p>
    <p class="para mgt-20">1. Retention period</p>
    <p class="para mgt-20">
      We will continue to store your personal information for you during your
      use of our products and services. If you cancel your account or
      voluntarily delete the above information, we will keep your information
      in accordance with the provisions of the Network Security Law and other
      laws and regulations. After you cancel your account or voluntarily
      delete the above information, we will not use your personal information
      commercially, but we may use your personal information after anonymizing
      it.
    </p>
    <p class="para mgt-20">2. Location of Storage</p>
    <p class="para mgt-20">
      Your personal information is stored in a geographic location as
      permitted by applicable law, for example, for users from mainland China,
      their personal information is stored on servers located in mainland
      China. We do not transfer your personal information across borders
      unless required or permitted by law. If you use technologies that may
      hide your true geographic location, such as VPN, or encounter objective
      circumstances such as inaccurate domain name or IP address resolution,
      you may be connected to a server outside of the above geographic
      location, at which time your personal information may be transferred
      across borders.
    </p>

    <p class="fw400 para mgt-20">
      (ii) Protection of your personal information
    </p>
    <p class="para mgt-20">
      1. In order to safeguard the security of your information, we endeavor
      to take various reasonable physical, electronic and administrative
      security measures to protect your information from leakage, destruction
      or loss, including but not limited to SSL, encrypted storage of
      information, and data center access control. We also adopt strict
      management of employees or outsourced personnel who may have access to
      your information, including but not limited to measures such as adopting
      different authority controls depending on the position, signing
      confidentiality agreements with them, and monitoring their operations.
      We will provide appropriate security measures to protect your
      information according to the existing technology and provide reasonable
      security, and we will try our best to make sure that your information
      will not be leaked, destroyed or lost.
    </p>
    <p class="para mgt-20">
      2. Your account is protected by security features, so please keep your
      account and password information safe. We will ensure that your
      information is not lost, misused or altered by backing up to other
      servers, encrypting your password, and other security measures.
      Notwithstanding the aforementioned security measures, please understand
      that there are no "perfect security measures" on the information
      network. 3.
    </p>
    <p class="para mgt-20">
      3. When using our platform services to conduct online transactions, you
      will inevitably disclose your personal information, such as bank account
      information, contact information or postal address, to the counterparty
      or potential counterparty. Please protect your personal information and
      provide it to others only when necessary. If you find that your personal
      information is leaked, especially your account and password, please
      contact our customer service immediately so that we can take appropriate
      measures.
    </p>

    <p class="bold para mgt-30">V. Use of Cookies</p>
    <p class="para mgt-20">
      In order to make your access experience easier, when you visit our
      platform website or use the services provided by our platform, we may
      identify you through small data files, which are used to help you save
      the step of repeatedly entering your registration information or to help
      determine the security of your account. These data files may be cookies,
      Flash cookies, or other local storage provided by your browser or
      associated applications (collectively, "Cookies").
    </p>
    <p class="para mgt-20">
      Please understand that some of our services are only available through
      the use of "cookies". You may modify your acceptance of cookies or
      refuse our cookies if your browser or browser add-on allows, but this
      action may, in some cases, affect your ability to safely access websites
      and use services offered on our Platform.
    </p>
    <p class="para mgt-20">
      Our products and services may have cookies and webbeacons placed by
      advertisers or other partners. these cookies and webbeacons may collect
      non-personally identifiable information about you for the purpose of
      analyzing how users use such services, sending you advertisements that
      may be of interest to you, or for evaluating the effectiveness of
      advertising services. The collection and use of such information by
      these third-party cookies and webbeacons is not governed by this Privacy
      Policy, but by the privacy policies of the relevant users, and we are
      not responsible for third-party cookies or webbeacons.
    </p>
    <p class="para mgt-20">
      You can refuse or manage cookies or webbeacons through your browser
      settings, but please note that if you disable cookies or webbeacons, you
      may not be able to enjoy a good service experience and some services may
      not work properly. You will also receive the same number of
      advertisements, but they will be less relevant to you.
    </p>

    <p class="bold para mgt-30">
      VI. Access, Modification and Deletion of Information
    </p>
    <p class="para mgt-20">
      You may access, modify and delete registration information and other
      personal information that you provide in the course of using our
      services. The scope and manner of your access, modification and deletion
      of personal information will depend on the specific service you use.
    </p>
    <p class="para mgt-20">
      We will collect and use your information as described in this policy
      solely to fulfill the functions of our products or services. If you find
      that we collect and use your personal information in violation of the
      laws and administrative regulations or the agreement between the
      parties, you can ask us to delete it. If you find that we have collected
      and stored your personal information in error, you can also ask us to
      correct it.
    </p>
    <p class="fw400 para mgt-20">
      (i) You can access, correct or delete your information by.
    </p>
    <p class="para mgt-20">
      (1) Logging into the App of the corresponding product or service and
      accessing, modifying or deleting it after entering the "Personal
      Center".
    </p>
    <p class="para mgt-20">
      (2) You can contact us through the external announcement mailbox of this
      platform (<a class="cl08c" href="mailto:<EMAIL>"><EMAIL></a>). After receiving the email, we
      will evaluate such requests
      according
      to the specific situation.
    </p>
    <p class="para mgt-20">
      (3) You may also submit your request for access, modification or
      deletion directly here. Upon receipt of the request, we will contact you
      to understand your specific requirements and meet your needs.<span class="link" id="btn">(Click here to submit
        your access, modification, or deletion
        information)</span>
    </p>

    <p class="fw400 para mgt-20">
      iOS users can request deletion of their data in the following ways.
    </p>
    <p class="para mgt-20">
      (1) Login to the APP, enter [My] - [Settings] - [Privacy Policy], find
      the clause [VI. Access, Modification and Deletion of Information] and
      contact us to submit a data deletion request.
    </p>
    <p class="para mgt-20">
      (2) You can also contact us through the platform's external announcement
      email (<a class="cl08c" href="mailto:<EMAIL>"><EMAIL></a>). After receiving the email, we will
      respond to your request
      according
      to the specific situation.
    </p>
    <p class="para mgt-20">
      (3) You can also submit your data deletion request directly here.
      <span class="link" id="btn1">(Click here to submit your data deletion request)</span>
    </p>
    <p class="fw400 para mgt-20">
      (ii) Change the scope of your authorized consent
    </p>
    <p class="para mgt-20">
      Each business function requires some basic personal information in order
      to be completed. You may give or withdraw your authorized consent from
      us by changing the settings of your smart mobile devices such as cell
      phones and tablets or by the means listed above. When you withdraw your
      consent, we will no longer process the relevant personal information.
      However, your decision to withdraw your consent will not affect the
      processing of personal information previously carried out on the basis
      of your authorization.
    </p>

    <p class="fw400 para mgt-20">
      (iii) How we respond to your request above
    </p>
    <p class="para mgt-20">
      When you access, modify or delete the relevant information or account,
      we may ask you to verify your identity in order to protect the security
      of the account.
    </p>
    <p class="para mgt-20">
      Please understand that due to technical limitations, legal or regulatory
      requirements, we may not be able to meet all your requests and we will
      respond to your requests within a reasonable period of time. Also, in
      accordance with relevant laws and regulations and national standards, we
      may not be able to respond to your request in the following
      circumstances.
    </p>
    <p class="para mgt-20">
      1. directly related to national security or national defense security
    </p>
    <p class="para mgt-20">
      2. directly related to public safety, public health, and significant
      public interests
    </p>
    <p class="para mgt-20">
      3. directly related to crime investigation, prosecution, trial and
      execution of judgments, etc.
    </p>
    <p class="para mgt-20">
      4. where there is sufficient evidence that you have subjective malice or
      abuse of rights
    </p>
    <p class="para mgt-20">
      5. where responding to your request will result in serious damage to the
      legitimate rights and interests of other individuals and organizations
    </p>
    <p class="para mgt-20">6. if it involves commercial secrets.</p>

    <p class="bold para mgt-30">
      VII. Special agreement on the privacy of minors
    </p>
    <p class="para mgt-20">
      We require parents or guardians to instruct minors under the age of
      eighteen (or other age of majority under applicable law, generally
      depending on the user's nationality and current country/region) to use
      our Services after reading this Privacy Policy.
    </p>
    <p class="para mgt-20">
      We do not knowingly collect personal information from minors. Minors
      should not register for an account or send us personal information such
      as their name, address, phone number, email address, etc., unless
      permitted by local law and with the consent of their guardian. If we
      accidentally collect information from minors, we will delete it as soon
      as we are aware of it. If you believe that we may improperly hold
      information about minors, please contact us as provided at the end of
      this Privacy Policy.
    </p>

    <p class="bold para mgt-30">VIII. Special Terms of Privacy Policy</p>
    <p class="para mgt-20">
      You are fully aware that your authorized consent is not required for us
      to collect or use personal information in the following circumstances.
    </p>
    <p class="para mgt-20">
      1. related to national security and social public interest
    </p>
    <p class="para mgt-20">
      2. in connection with crime investigation, prosecution, trial and
      execution of judgment, etc.
    </p>
    <p class="para mgt-20">
      3. for the purpose of safeguarding the life, property and other
      significant legitimate rights and interests of the subject of the
      personal information or other individuals but where it is difficult to
      obtain the consent of the individual.
    </p>
    <p class="para mgt-20">
      4. where the personal information collected is disclosed to the public
      by the subject of the personal information himself/herself
    </p>
    <p class="para mgt-20">
      5. where your personal information is collected from information that is
      lawfully and publicly disclosed, such as lawful news reports, government
      information disclosure and other channels.
    </p>
    <p class="para mgt-20">
      6. the violation of legal provisions or violation of our platform rules
      lead us to have taken necessary measures against you.
    </p>
    <p class="para mgt-20">
      7. necessary to enter into a contract at your request
    </p>
    <p class="para mgt-20">
      8. necessary to maintain the safe and stable operation of the products
      or services provided, such as the detection and disposal of product or
      service failures.
    </p>
    <p class="para mgt-20">9. necessary for legitimate press coverage.</p>
    <p class="para mgt-20">
      10. necessary for academic research institutions to conduct statistical
      or academic research in the public interest and to de-identify the
      personal information contained in the results when providing the results
      of academic research or descriptions to the public
    </p>
    <p class="para mgt-20">
      11. other circumstances as stipulated by laws and regulations.
    </p>

    <p class="bold para mgt-30">IX. Changes to the Privacy Policy</p>
    <p class="para mgt-20">
      We may revise the terms of this Privacy Policy from time to time, and
      such revisions will form part of this Privacy Policy. If such revisions
      result in a material reduction of your rights under this Privacy Policy,
      we will notify you by prominently displaying the revisions on our home
      page or by sending you an email or otherwise before the revisions take
      effect. In such event, by continuing to use our Services, you agree to
      be bound by this Privacy Policy, as amended.
    </p>

    <p class="bold para mgt-30">X. How to contact us</p>
    <p class="para mgt-20">You can contact us through the following ways.</p>
    <p class="para mgt-20">
      1. If you have any questions, comments or suggestions about the content
      of this policy, you can send an email to contact us.
    </p>
    <p class="para mgt-20">
      E-mail: <a class="cl08c" href="mailto:<EMAIL>"><EMAIL></a>
    </p>
    <p class="para mgt-20">Company Name: Wenova Limited</p>
    <p class="grey para mgt-20">
      Note: This version of the Privacy Policy was updated on February 26,
      2021.
    </p>

  </div>

  <div class="van-overlay" id="overlay">
    <div role="dialog" class="van-dialog" style="z-index: 2002">
      <div class="header">
        <div>Submit a Request</div>
        <div class="desc">Modify Delete Data</div>
      </div>
      <div class="content">
        <div class="van-cell__title"><span>Contact Information</span></div>
        <div class="van-field__body">
          <input type="text" name="Contact Information" id="input" placeholder="Please enter your contact information"
            class="van-field__control" />
        </div>
        <div class="inputText" id="inputText">
          Please enter your contact information
        </div>
        <div class="van-cell__title">
          <span>Requirement Description</span>
        </div>
        <div class="van-field__body textarea_body">
          <textarea rows="2" id="textarea" placeholder="Please enter your request (e.g delete my data)"
            class="van-field__control textarea"></textarea>
          <div class="num" id="num">0/100</div>
        </div>
        <div class="textareaText" id="textareaText">
          Please enter your contact information
        </div>
      </div>
      <div class="footer">
        <div class="buttom Cancel" id="Cancel">Cancel</div>
        <div class="buttom Submit" id="Submit">Submit</div>
      </div>
    </div>
  </div>
  <div class="toast">
    <p>Submitted successfully,we will process your request later</p>
  </div>
</body>

</html>
<script>
  const btn = document.querySelector("#btn");
  const btn1 = document.querySelector("#btn1");
  const overlay = document.querySelector("#overlay");
  const Cancel = document.querySelector("#Cancel");
  const Submit = document.querySelector("#Submit");
  const input = document.querySelector("#input");
  const textarea = document.querySelector("#textarea");
  const inputText = document.querySelector("#inputText");
  const textareaText = document.querySelector("#textareaText");
  const num = document.querySelector("#num");
  btn.addEventListener("click", function (e) {
    e.preventDefault();
    input.value = "";
    textarea.value = "";
    overlay.style.display = "flex";
  });
  btn1.addEventListener("click", function (e) {
    e.preventDefault();
    input.value = "";
    textarea.value = "";
    overlay.style.display = "flex";
  });
  input.addEventListener("blur", function (e) {
    input.value = input.value.trim();
    if (input.value == "") {
      inputText.style.display = "block";
    } else {
      inputText.style.display = "none";
    }
  });
  textarea.addEventListener("blur", function (e) {
    textarea.value = textarea.value.trim();
    if (textarea.value == "") {
      textareaText.style.display = "block";
    } else {
      textareaText.style.display = "none";
    }

    console.log(textarea.value.length);
  });
  textarea.addEventListener("input", function (e) {
    if (textarea.value.length >= 1000) {
      textarea.value = textarea.value.slice(0, 100);
      textarea.dispatchEvent(new Event("input", { bubbles: true }));
    }
    num.innerHTML = textarea.value.length + "/100";
  });
  Cancel.addEventListener("click", function (e) {
    e.preventDefault();
    overlay.style.display = "none";
    textareaText.style.display = "none";
    inputText.style.display = "none";
    num.innerHTML = "0/100";
    document.querySelector("html").style.overflow = "auto";
    document.querySelector("html").style.height = "100%";
  });
  overlay.addEventListener("touchstart", function (e) {
    document.querySelector("html").style.overflow = "hidden";
    document.querySelector("html").style.height = "100vh";
  });

  Submit.addEventListener("click", function (e) {
    e.preventDefault();
    if (textareaText.value != "" && input.value != "") {
      overlay.style.display = "none";
      num.innerHTML = "0/100";
      document.querySelector("html").style.overflow = "auto";
      document.querySelector("html").style.height = "100%";
      document.querySelector(".toast").style.display = "flex";
      setTimeout(function () {
        document.querySelector(".toast").style.display = "none";
      }, 2000);
    } else {
      if (textarea.value == "") {
        textareaText.style.display = "block";
      }
      if (input.value == "") {
        inputText.style.display = "block";
      }
    }
  });
  const close = document.querySelector('#close')
  close.addEventListener('click', () => {
    window.location.href = "https://wowed.live/wowlive/index.html"+window.location.search;
  })
  const logo = document.querySelector('#logo')
  logo.addEventListener("click", (e) => {
    e.preventDefault();
    window.location.href = "https://wowed.live/wowlive/index.html"+window.location.search;
  });

  const terms = document.querySelector("#terms");
  terms.addEventListener("click", (e) => {
    e.preventDefault();
    window.location.href = "https://wowed.live/wowlive/service.html"+window.location.search;
  });

</script>