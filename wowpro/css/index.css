* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
img {
  display: block;
}
html,
body {
  font-family: Roboto-Regular, <PERSON><PERSON>;
  position: relative;
  width: 100%;
  height: 100%;
  background: url("../imgs/bg.png") no-repeat;
  background-size: cover;
  background-position: center center;
  overflow: hidden;
}

header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0 54px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10px);
  z-index: 2;
}
.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  color: #8524ff;
}
.logo img {
  width: 48px;
  height: 48px;
  margin-right: 12px;
}
.right {
  display: flex;
  align-items: center;
}
header .right .item {
  font-size: 16px;
  padding: 0 19px;
  color: #8524ff;
}
header .right .item .text {
  font-size: 16px;
  color: #8524ff;
  background: transparent;
  text-decoration: none;
  cursor: pointer;
}
header .right .item:nth-child(2n) {
  border-right: 1px solid rgba(155, 50, 254, 0.2);
  border-left: 1px solid rgba(155, 50, 254, 0.2);
}

.center {
  display: flex;
  height: 100%;
  width: 100%;
}

.center .left {
  height: 100%;
  width: 40%;
  display: flex;
  flex-direction: column;

  justify-content: center;
  padding-left: 5%;
}
.center .left .meta {
  padding: 0.21rem 0;
}
.center .left .meta img {
  display: none;
  width: 0.42rem;
  height: 0.37rem;
}
.center .left .title {
  font-weight: 900;
  font-size: 69px;
  color: #8524ff;
  margin-bottom: 0px;
}
.center .left .icon {
  display: none;
}
.center .left .icon p {
  font-size: 0.56rem;
  color: #1d0041;
  font-weight: bold;
  line-height: 0.7rem;
}
.center .left .icon .p2 {
  display: flex;
  align-items: end;
}
.center .left .p3 {
  font-size: 16px;
  color: rgba(29, 0, 65, 0.77);
}
.center .left .icon .icon-box {
  display: flex;
  justify-content: center;
  align-items: center;

  position: relative;
}
.center .left .icon .p2 p {
  margin-right: 0.2rem;
}
.center .left .icon .icon-box .icon2 {
  width: 0.84rem;
  height: 0.84rem;
}
.center .left .icon .icon-box .icon3 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 0.84rem;
  height: 0.26rem;
}
.center .left p {
  font-size: 32px;
  color: rgba(29, 0, 65, 0.77);
}
.center .left .p1 {
  margin-top: 15px;
}

.center .left .download {
  display: flex;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 22px;
}
.center .left .download .apple_store {
  width: 40%;

  margin-right: 21px;
}
.center .left .download .apk {
  width: 40%;
}
.center .left .text {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: rgba(51, 51, 51, 0.7);
  cursor: pointer;
}
.center .left .text span {
  border-bottom: 1px solid rgba(51, 51, 51, 0.7);
}
.center .left .text img {
  width: 25px;
  height: 25px;
}
.center .right {
  width: 60%;
  height: 870px;
  padding: 0 20px;
  margin-top: 70px;
  overflow: hidden;
  background-color: #fff;
  border: 3px solid #ffffff;
  border-radius: 24px;
  position: relative;
}
.center .right .item {
  width: 23%;
  height: 870px;
  margin: 0 12px;
}
.center .right img {
  width: 100%;

  overflow: hidden;
  display: block;
  padding: 10px 0;
  object-fit: cover;
}

.footer {
  position: fixed;
  left: 54px;
  bottom: 34px;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  opacity: 0.5;
}

.center .right1 {
  width: 100vw;

  margin-top: 0.4rem;
  overflow: hidden;
  background-color: transparent;
  border: none;
  display: none;
}
.center .right1 .item {
  margin-top: 0.2rem;
  display: flex;
}
.center .right1 img {
  height: 3rem;
  display: inline-block;
  padding: 0 0.1rem;
  object-fit: cover;
}

.center .download1 {
  display: none;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 22px;
}
.center .download1 .apple_store {
  width: 4.4rem;
  height: 1rem;
  margin-bottom: 0.33rem;
}
.center .download1 .apk {
  width: 4.4rem;
  height: 1rem;
}

.center .text1 {
  display: none;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  color: rgba(51, 51, 51, 0.7);
}
.center .text1 span {
  border-bottom: 1px solid rgba(51, 51, 51, 0.7);
}
.center .left .text img {
  width: 25px;
  height: 25px;
}

.make {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.74);
  backdrop-filter: blur(50px);
  z-index: 9;
  display: none;
}
.make .close {
  position: fixed;
  top: 0.22rem;
  left: 0.4rem;
  width: 0.52rem;
  height: 0.52rem;
  z-index: 10;
}
.make .close img {
  width: 0.52rem;
  height: 0.52rem;
}
.make .make-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 0.4rem;
}
.make .make-box .item {
  width: 100%;
  font-size: 0.36rem;
  padding: 0.4rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.81);
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.make .make-box .item .text {
  font-size: 0.36rem;
  color: rgba(0, 0, 0, 0.9);
  background: transparent;
  text-decoration: none;
  cursor: pointer;
}
.make .make-box .home {
  color: rgba(133, 36, 255, 1);
}
.make .make-box .home .text {
  color: rgba(133, 36, 255, 1);
}
#video {
  position: fixed;
  top: 50%;
  left: 20%;
  z-index: 99;
  width: 369px;
  height: 770px;
  border: 11px solid #262626;
  border-radius: 35px;
  overflow: hidden;
  transform: translateY(-50%);
  display: none;
  background: #d8d8d8;
}
video {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  object-fit: fill;
  -webkit-object-fit: fill;
}

#meta {
  display: none;
  width: 0.42rem;
  height: 0.37rem;
}
#video_close {
  position: fixed;
  top: 12px;
  right: 0;
  width: 52px;
  height: 52px;
  z-index: 999;
}
#video_close img {
  width: 32px;
  height: 32px;
}

.footerText{
  font-weight: 400;
  font-size: 12px;
  color: #1D0041;
  line-height: 14px;
  text-align: center;
  font-style: normal;
  display: none;
}

