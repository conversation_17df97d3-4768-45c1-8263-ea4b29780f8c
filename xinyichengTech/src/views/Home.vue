<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const isMenuOpen = ref(false)
const currentSlide = ref(0)
const images = [
  'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=1200&q=80',
  'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80',
  'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=1200&q=80'
]
let intervalId = null

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const goToSlide = (idx) => {
  currentSlide.value = idx
}

onMounted(() => {
  intervalId = setInterval(() => {
    currentSlide.value = (currentSlide.value + 1) % images.length
  }, 4000)
})

onUnmounted(() => {
  clearInterval(intervalId)
})
</script>

<script>
export default {
  name: 'HomePage'
}
</script>

<template>
  <div id="home">
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <router-link to="/" class="logo-link">
            <img src="/logo.png" alt="XinYiChengTech" class="logo-image">
          </router-link>
        </div>
        
        <div class="nav-menu" :class="{ 'active': isMenuOpen }">
          <a href="#home" class="nav-link">HOME</a>
          <a href="#about" class="nav-link">ABOUT</a>
          <a href="#app-section" class="nav-link">APP</a>
          <a href="#benefits" class="nav-link">BENEFITS</a>
          <a href="#contact" class="nav-link">CONTACT</a>
        </div>
        
        <div class="nav-toggle" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-slider">
        <div
          v-for="(img, idx) in images"
          :key="img"
          class="hero-slide"
          :style="{ backgroundImage: `url(${img})`, opacity: currentSlide === idx ? 1 : 0 }"
        ></div>
        <div class="hero-overlay"></div>
      </div>
      <div class="hero-content">
        <h1 class="hero-title">CONNECT SIMPLE HAPPY</h1>
        <p class="hero-subtitle">
          With the mission of "creating value for users", based on the global market, 
          we are committed to providing innovative social connection solutions for the digital age.
        </p>
        <div class="hero-indicators">
          <div
            v-for="(img, idx) in images"
            :key="'indicator-' + idx"
            class="indicator"
            :class="{ active: currentSlide === idx }"
            @click="goToSlide(idx)"
          ></div>
        </div>
      </div>
    </section>

    <!-- About Us -->
    <section id="about" class="about">
      <div class="container">
        <h2 class="section-title">ABOUT</h2>
        <div class="section-content">
          <div class="content-card large-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=500&q=80" alt="Team collaboration">
            </div>
            <div class="card-content">
              <div class="quote-mark">"</div>
              <p>
                With the mission of <span class="highlight-text">creating value for users</span>, the company is committed to 
                the development and operation of overseas Internet cultural and creative products. 
                With the core value of <span class="highlight-text">creating value for our partners</span> and the enterprise 
                spirit of <span class="highlight-text">honesty, tolerance, innovation and service</span>, we create value for the 
                Internet and information service industry through independent innovation and sincere cooperation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- App Section -->
    <section id="app-section" class="app">
      <div class="container">
        <h2 class="section-title">App</h2>
        <div class="section-content">
          <div class="content-card app-card">
            <div class="card-image">
              <img src="/background.png" alt="App Team">
            </div>
            <div class="card-content">
              <div class="app-icon">
                <img src="/app_logo.png" alt="App Icon" class="app-icon-img">
              </div>
              <h3>MeetU-Share Your Moments</h3>
              <p>
                Meet like-minded friends, discover new and interesting things, chat with them,
                and share your thoughts at the moment
              </p>
              <div class="download-section">
                <a href="https://apps.apple.com/us/app/id6450502758?l=en-us">
                  <img src="/download_button.png" alt="Download on the App Store" class="download-btn">
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Company Benefits -->
    <section id="benefits" class="benefits">
      <div class="container">
        <h2 class="section-title">COMPANY BENEFITS</h2>
        <div class="section-content">
          <div class="content-grid">
            <div class="content-card benefit-card">
              <div class="card-number">1</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574?auto=format&fit=crop&w=400&q=80" alt="Afternoon Tea">
              </div>
              <div class="card-content">
                <h3>Afternoon Tea</h3>
              </div>
            </div>
            <div class="content-card benefit-card">
              <div class="card-number">2</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?auto=format&fit=crop&w=400&q=80" alt="Team Retreat">
              </div>
              <div class="card-content">
                <h3>Team Retreat</h3>
              </div>
            </div>
            <div class="content-card benefit-card">
              <div class="card-number">3</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1497032628192-86f99bcd76bc?auto=format&fit=crop&w=400&q=80" alt="Flexible Work">
              </div>
              <div class="card-content">
                <h3>Flexible Work</h3>
              </div>
            </div>
            <div class="content-card benefit-card">
              <div class="card-number">4</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=400&q=80" alt="Fitness Center">
              </div>
              <div class="card-content">
                <h3>Fitness Center</h3>
              </div>
            </div>
            <div class="content-card benefit-card">
              <div class="card-number">5</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?auto=format&fit=crop&w=400&q=80" alt="Learning Budget">
              </div>
              <div class="card-content">
                <h3>Learning Budget</h3>
              </div>
            </div>
            <div class="content-card benefit-card">
              <div class="card-number">6</div>
              <div class="card-image">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?auto=format&fit=crop&w=400&q=80" alt="Paid Vacation">
              </div>
              <div class="card-content">
                <h3>Paid Vacation</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Us -->
    <section id="contact" class="contact">
      <div class="container">
        <h2 class="section-title">CONTACT</h2>
        <div class="section-content">
          <div class="content-grid contact-grid">
            <div class="content-card contact-card">
              <div class="card-icon">
                <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
              <div class="card-content">
                <h3>Office Address</h3>
                <p>武汉东湖新技术开发区高新二路266号<br>武汉当代北辰置业有限公司P（2014）57号地块一期<br>1号商业栋/单元-1-2层羽毛球馆号1层05号</p>
              </div>
            </div>
            <div class="content-card contact-card">
              <div class="card-icon">
                <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </div>
              <div class="card-content">
                <h3>Contact Email</h3>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <router-link to="/" class="logo-link">
              <img src="/logo.png" alt="XinYiChengTech" class="logo-image">
            </router-link>
          </div>
          <div class="footer-links">
            <a href="#home">HOME</a>
            <a href="#about">ABOUT</a>
            <a href="#app-section">APP</a>
            <a href="#contact">CONTACT</a>
            <router-link to="/privacy" class="footer-policy">Privacy Policy</router-link>
            <router-link to="/terms" class="footer-policy">Terms and Conditions</router-link>
          </div>
          <div class="footer-social">
            <a href="https://facebook.com" target="_blank" rel="noopener" aria-label="Facebook" class="social-icon">
              <svg viewBox="0 0 24 24" width="28" height="28" fill="currentColor"><path d="M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.408.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.408 24 22.674V1.326C24 .592 23.406 0 22.675 0"/></svg>
            </a>
            <a href="https://linkedin.com" target="_blank" rel="noopener" aria-label="LinkedIn" class="social-icon">
              <svg viewBox="0 0 24 24" width="28" height="28" fill="currentColor"><path d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11.75 20h-3v-10h3v10zm-1.5-11.25c-.966 0-1.75-.784-1.75-1.75s.784-1.75 1.75-1.75 1.75.784 1.75 1.75-.784 1.75-1.75 1.75zm15.25 11.25h-3v-5.5c0-1.104-.896-2-2-2s-2 .896-2 2v5.5h-3v-10h3v1.354c.417-.646 1.19-1.354 2.25-1.354 1.654 0 3 1.346 3 3v7z"/></svg>
            </a>
            <a href="https://instagram.com" target="_blank" rel="noopener" aria-label="Instagram" class="social-icon">
              <svg viewBox="0 0 24 24" width="28" height="28" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 1.366.062 2.633.334 3.608 1.308.974.974 1.246 2.242 1.308 3.608.058 1.266.069 1.646.069 4.85s-.012 3.584-.07 4.85c-.062 1.366-.334 2.633-1.308 3.608-.974.974-2.242 1.246-3.608 1.308-1.266.058-1.646.069-4.85.069s-3.584-.012-4.85-.07c-1.366-.062-2.633-.334-3.608-1.308-.974-.974-1.246-2.242-1.308-3.608C2.175 15.647 2.163 15.267 2.163 12s.012-3.584.07-4.85c.062-1.366.334-2.633 1.308-3.608.974-.974 2.242-1.246 3.608-1.308C8.416 2.175 8.796 2.163 12 2.163zm0-2.163C8.741 0 8.332.013 7.052.072 5.771.131 4.659.363 3.678 1.344c-.98.98-1.212 2.092-1.271 3.373C2.013 5.668 2 6.077 2 12c0 5.923.013 6.332.072 7.613.059 1.281.291 2.393 1.271 3.373.98.98 2.092 1.212 3.373 1.271C8.332 23.987 8.741 24 12 24s3.668-.013 4.948-.072c1.281-.059 2.393-.291 3.373-1.271.98-.98 1.212-2.092 1.271-3.373.059-1.281.072-1.69.072-7.613 0-5.923-.013-6.332-.072-7.613-.059-1.281-.291-2.393-1.271-3.373-.98-.98-2.092-1.212-3.373-1.271C15.668.013 15.259 0 12 0zm0 5.838a6.162 6.162 0 1 0 0 12.324 6.162 6.162 0 0 0 0-12.324zm0 10.162a3.999 3.999 0 1 1 0-7.998 3.999 3.999 0 0 1 0 7.998zm6.406-11.845a1.44 1.44 0 1 0 0 2.881 1.44 1.44 0 0 0 0-2.881z"/></svg>
            </a>
          </div>
        </div>
        <div class="footer-bottom">
          <p>Copyright &copy;2025 Wuhan Xinyicheng Network Technology Co., Ltd.</p>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <div class="scroll-top">
      <button class="scroll-btn">^</button>
    </div>
  </div>
</template>

<style scoped>
/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #ffffff;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-link {
  text-decoration: none;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 10, 15, 0.9);
  backdrop-filter: blur(20px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
}

.logo-image {
  width: 150px;
  transition: transform 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  gap: 3rem;
}

.nav-link {
  text-decoration: none;
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: color 0.3s ease;
  text-transform: uppercase;
}

.nav-link:hover {
  color: #00d4ff;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: #ffffff;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, transparent 0%, rgba(0, 212, 255, 0.05) 50%, transparent 100%);
  z-index: 4;
  pointer-events: none;
}

.hero-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}
.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: opacity 1s cubic-bezier(.4,0,.2,1);
  opacity: 0;
  z-index: 1;
}
.hero-slide:nth-child(1) { z-index: 2; }
.hero-slide:nth-child(2) { z-index: 2; }
.hero-slide:nth-child(3) { z-index: 2; }
.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10,10,15,0.8) 0%, rgba(26,26,46,0.7) 50%, rgba(22,33,62,0.8) 100%);
  z-index: 3;
  pointer-events: none;
  backdrop-filter: blur(2px);
}
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1200px;
  padding: 0 20px;
  margin: 0 auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.hero-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 2rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  background: linear-gradient(45deg, #ffffff, #00d4ff, #ffffff, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 300% 300%;
  animation: gradientShift 4s ease-in-out infinite;
  text-shadow: 0 0 40px rgba(0, 212, 255, 0.3);
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  animation: shimmer 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
  100% { transform: translateX(100%); }
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 3rem;
  color: #cccccc;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-indicators {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: linear-gradient(45deg, #00d4ff, #7c3aed);
  border: 2px solid #ffffff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

/* Section Styles */
.section-title {
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 4rem;
  background: linear-gradient(45deg, #00d4ff, #ffffff, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  letter-spacing: 3px;
  position: relative;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

.section-title::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #00d4ff 20%, #7c3aed 80%, transparent 100%);
  z-index: -1;
}

.section-title::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -2;
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
}

.about, .app, .benefits, .contact {
  padding: 120px 0;
  background: transparent;
  position: relative;
  scroll-margin-top: 100px;
}

.about::before, .app::before, .benefits::before, .contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Card System */
.content-card {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(22, 33, 62, 0.8) 100%);
  border-radius: 24px;
  border: 1px solid rgba(0, 212, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.content-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.3);
}

.content-card:hover::before {
  opacity: 1;
}

/* Grid System */
.content-grid {
  display: grid;
  gap: 2rem;
}

/* About Section */
.large-card {
  display: flex;
  align-items: center;
  gap: 3rem;
  padding: 3rem;
  max-width: 1160px;
  margin: 0 auto;
}

.large-card .card-image {
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.large-card .card-image img {
  width: 400px;
  height: 280px;
  object-fit: cover;
  border-radius: 16px;
  transition: transform 0.4s ease;
}

.content-card:hover .card-image img {
  transform: scale(1.02);
}

.large-card .card-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.quote-mark {
  font-size: 4rem;
  color: #00d4ff;
  opacity: 0.3;
  position: absolute;
  top: -20px;
  left: 0;
  font-family: serif;
  pointer-events: none;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.large-card .card-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #ffffff;
  font-weight: 400;
  margin-top: 1rem;
}

.highlight-text {
  background: linear-gradient(45deg, #00d4ff, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* App Section */
.app-card {
  display: flex;
  align-items: center;
  gap: 3rem;
  padding: 3rem;
  max-width: 1160px;
  margin: 0 auto;
  background: rgba(16, 24, 40, 0.6);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.app-card .card-image {
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.app-card .card-image img {
  width: 400px;
  height: 280px;
  object-fit: cover;
  border-radius: 16px;
  transition: transform 0.4s ease;
}

.app-card .card-content {
  flex: 1;
  position: relative;
  z-index: 2;
  text-align: left;
}

.app-icon {
  margin-bottom: 1rem;
}

.app-icon-img {
  width: 60px;
  height: 60px;
  border-radius: 12px;
}

.app-card .card-content h3 {
  font-size: 1.8rem;
  color: #ffffff;
  margin-bottom: 1rem;
  font-weight: 600;
}

.app-card .card-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #ffffff;
  font-weight: 400;
  margin-bottom: 2rem;
}

.download-section {
  margin-top: 1.5rem;
}

.download-btn {
  height: 60px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.download-btn:hover {
  transform: scale(1.05);
}

/* Benefits Section */
.benefits .content-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.benefit-card {
  padding: 0;
}

.card-number {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: #ffffff;
  z-index: 3;
  transition: transform 0.4s ease;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.content-card:hover .card-number {
  transform: scale(1.1);
}

.benefit-card .card-image {
  height: 220px;
  position: relative;
  z-index: 2;
}

.benefit-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.benefit-card .card-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.benefit-card .card-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  letter-spacing: 0.5px;
}

/* Contact Section */
.contact-grid {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  max-width: 900px;
  margin: 0 auto;
}

.contact-card {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 2.5rem;
}

.card-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  flex-shrink: 0;
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
}

.content-card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4), 0 0 40px rgba(124, 58, 237, 0.3);
}

.contact-card .card-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.contact-card .card-content h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

.contact-card .card-content p {
  color: #cccccc;
  line-height: 1.6;
  font-size: 1rem;
  font-weight: 400;
}

/* Footer */
.footer {
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95) 0%, rgba(22, 33, 62, 0.9) 100%);
  padding: 60px 0 20px;
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.footer-links {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #00d4ff;
}

.footer-policy {
  color: #cccccc;
  text-decoration: underline;
  font-size: 0.9rem;
  margin-left: 1rem;
}
.footer-policy:hover {
  color: #00d4ff;
}

.footer-social {
  display: flex;
  gap: 1.2rem;
}
.social-icon {
  color: #cccccc;
  transition: color 0.3s;
  display: flex;
  align-items: center;
}
.social-icon:hover {
  color: #00d4ff;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo-image {
    max-height: 45px;
  }

  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 15, 0.95);
    flex-direction: column;
    padding: 2rem;
    backdrop-filter: blur(20px);
    transform: translateY(-100%);
    opacity: 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .footer-links {
    gap: 1rem;
  }
  .footer-social {
    margin-top: 1rem;
  }

  .section-content {
    padding: 0 1rem;
  }
  
  .large-card {
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
  }
  
  .large-card .card-image img {
    width: 100%;
    max-width: 400px;
    height: 220px;
  }

  .app-card {
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
  }

  .app-card .card-image img {
    width: 100%;
    max-width: 400px;
    height: 220px;
  }

  .benefits .content-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .benefit-card .card-image {
    height: 180px;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .contact-card {
    padding: 2rem;
    gap: 1.5rem;
  }
  
  .card-icon {
    width: 70px;
    height: 70px;
  }
}

/* Back to Top Button */
.scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.scroll-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(26, 26, 46, 0.9);
  border: 1px solid rgba(0, 212, 255, 0.2);
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.scroll-btn:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}
</style> 